:root {
	/* --- colors --- */
	--background: #f7f7f7;
	--foreground: #000000;
	--shadow: rgba(0, 0, 0, 0.05);

	/* input */
	--input-surface: rgba(0, 0, 0, 0.06);
	--input-surface-focus: rgba(0, 0, 0, 0.08);

	/* button */
	--button-surface: #2e2f32;
	--button-surface-hover: #19191a;

	--surface: #ffffff;

	/* --- fonts --- */
	/* title */
	--font-title-size: 1.75rem;
	--font-title-weight: 700;
	--font-title-line-height: 100%;
	--font-title-letter-spacing: 0;

	/* subtitle */
	--font-subtitle-size: 1.25rem;
	--font-subtitle-weight: 600;
	--font-subtitle-line-height: 1.5rem;
	--font-subtitle-letter-spacing: -0.0125rem;

	/* body */
	--font-body-size: 1.0625rem;
	--font-body-weight: 400;
	--font-body-line-height: 1.375rem;
	--font-body-letter-spacing: -0.0075rem;

	/* caption */
	--font-caption-size: 0.625rem;
	--font-caption-weight: 400;
	--font-caption-line-height: 0.875rem;
	--font-caption-letter-spacing: 0.0075rem;
}

html,
body {
	max-width: 100vw;
	overflow-x: hidden;
}

html {
	font-size: 16px;
}

body {
	color: var(--foreground);
	background: var(--background);
	font-family: 'Inter', Helvetica, sans-serif;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

* {
	box-sizing: border-box;
	padding: 0;
	margin: 0;
}

a {
	color: inherit;
	text-decoration: none;
}

input {
	font-family: inherit;
	font-size: inherit;
	border: none;
	outline: none;
	background: none;
}

button {
	cursor: pointer;
	border: none;
	outline: none;
	background: none;
	font-family: inherit;
	font-size: inherit;
}
