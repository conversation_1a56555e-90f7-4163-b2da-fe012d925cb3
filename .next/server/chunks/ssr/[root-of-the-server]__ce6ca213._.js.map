{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/icons/search.tsx"], "sourcesContent": ["import Image from 'next/image'\nimport { FC } from 'react'\n\nconst SearchIcon: FC = () => {\n\treturn (\n\t\t<Image src={'/search-icon.svg'} alt='Search' width={20} height={20} />\n\t)\n}\n\nexport default SearchIcon\n"], "names": [], "mappings": ";;;;AAAA;;;AAGA,MAAM,aAAiB;IACtB,qBACC,8OAAC,6HAAA,CAAA,UAAK;QAAC,KAAK;QAAoB,KAAI;QAAS,OAAO;QAAI,QAAQ;;;;;;AAElE;uCAEe", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/shared/ui/input/index.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"input\": \"index-module__Bu1eGa__input\",\n  \"label\": \"index-module__Bu1eGa__label\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/input/index.tsx"], "sourcesContent": ["import { FC, HTMLInputTypeAttribute, ReactNode } from 'react'\nimport style from './index.module.css'\n\ninterface InputProps {\n\ttype?: HTMLInputTypeAttribute\n\tname?: string\n\tvalue?: string\n\tplaceholder?: string\n\tprefix?: ReactNode\n\tonChange?: (event: React.ChangeEvent<HTMLInputElement>) => void\n}\n\nconst Input: FC<InputProps> = ({\n\ttype,\n\tname,\n\tvalue,\n\tplaceholder,\n\tprefix,\n\tonChange\n}) => {\n\treturn (\n\t\t<label className={style.label}>\n\t\t\t{prefix}\n\t\t\t<input\n\t\t\t\tclassName={style.input}\n\t\t\t\ttype={type}\n\t\t\t\tname={name}\n\t\t\t\tvalue={value}\n\t\t\t\tplaceholder={placeholder}\n\t\t\t\tonChange={onChange}\n\t\t\t/>\n\t\t</label>\n\t)\n}\n\nexport default Input\n"], "names": [], "mappings": ";;;;AACA;;;AAWA,MAAM,QAAwB,CAAC,EAC9B,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,WAAW,EACX,MAAM,EACN,QAAQ,EACR;IACA,qBACC,8OAAC;QAAM,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;;YAC3B;0BACD,8OAAC;gBACA,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;gBACtB,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;;;;;;;;;;;;AAId;uCAEe", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/model/searchStore.ts"], "sourcesContent": ["import { create } from 'zustand'\n\ninterface SearchStore {\n\tsearchQuery: string\n\tsetSearchQuery: (value: string) => void\n}\n\nexport const useSearchStore = create<SearchStore>(set => ({\n\tsearchQuery: '',\n\tsetSearchQuery: value => set({ searchQuery: value })\n}))\n"], "names": [], "mappings": ";;;AAAA;;AAOO,MAAM,iBAAiB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAe,CAAA,MAAO,CAAC;QACzD,aAAa;QACb,gBAAgB,CAAA,QAAS,IAAI;gBAAE,aAAa;YAAM;IACnD,CAAC", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/hero/ui/index.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"dropdown\": \"index-module__m10heG__dropdown\",\n  \"hero\": \"index-module__m10heG__hero\",\n  \"notFound\": \"index-module__m10heG__notFound\",\n  \"search\": \"index-module__m10heG__search\",\n  \"title\": \"index-module__m10heG__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/searchDropdown.tsx"], "sourcesContent": ["import { FC } from 'react'\nimport style from './index.module.css'\n\ninterface SearchDropdownProps {\n\tsearchQuery: string\n}\n\nconst SearchDropdown: FC<SearchDropdownProps> = ({ searchQuery }) => {\n\treturn (\n\t\t<div className={style.dropdown} data-is-visible={!!searchQuery.length}>\n\t\t\t<span className={style.notFound}>Ничего не найдено :(</span>\n\t\t</div>\n\t)\n}\n\nexport default SearchDropdown\n"], "names": [], "mappings": ";;;;AACA;;;AAMA,MAAM,iBAA0C,CAAC,EAAE,WAAW,EAAE;IAC/D,qBACC,8OAAC;QAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,QAAQ;QAAE,mBAAiB,CAAC,CAAC,YAAY,MAAM;kBACpE,cAAA,8OAAC;YAAK,WAAW,iJAAA,CAAA,UAAK,CAAC,QAAQ;sBAAE;;;;;;;;;;;AAGpC;uCAEe", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/search.tsx"], "sourcesContent": ["'use client'\n\nimport SearchIcon from '@/src/shared/ui/icons/search'\nimport Input from '@/src/shared/ui/input'\nimport { useSearchStore } from '../model/searchStore'\nimport style from './index.module.css'\nimport SearchDropdown from './searchDropdown'\n\nconst HeroSearch = () => {\n\tconst { searchQuery, setSearchQuery } = useSearchStore()\n\n\tconst handleSearch = (value: string) => {\n\t\tsetSearchQuery(value)\n\t\tconsole.log(value)\n\t}\n\n\treturn (\n\t\t<div className={style.search}>\n\t\t\t<Input\n\t\t\t\tplaceholder='Найти направление'\n\t\t\t\tprefix={<SearchIcon />}\n\t\t\t\tvalue={searchQuery}\n\t\t\t\tonChange={value => handleSearch(value.target.value)}\n\t\t\t/>\n\t\t\t<SearchDropdown searchQuery={searchQuery} />\n\t\t</div>\n\t)\n}\n\nexport default HeroSearch\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,aAAa;IAClB,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,iBAAc,AAAD;IAErD,MAAM,eAAe,CAAC;QACrB,eAAe;QACf,QAAQ,GAAG,CAAC;IACb;IAEA,qBACC,8OAAC;QAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,MAAM;;0BAC3B,8OAAC,sIAAA,CAAA,UAAK;gBACL,aAAY;gBACZ,sBAAQ,8OAAC,uIAAA,CAAA,UAAU;;;;;gBACnB,OAAO;gBACP,UAAU,CAAA,QAAS,aAAa,MAAM,MAAM,CAAC,KAAK;;;;;;0BAEnD,8OAAC,+IAAA,CAAA,UAAc;gBAAC,aAAa;;;;;;;;;;;;AAGhC;uCAEe", "debugId": null}}]}