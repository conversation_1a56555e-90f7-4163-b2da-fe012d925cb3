{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/icons/search.tsx"], "sourcesContent": ["import Image from 'next/image'\nimport { FC } from 'react'\n\nconst SearchIcon: FC = () => {\n\treturn (\n\t\t<Image src={'/search-icon.svg'} alt='Search' width={20} height={20} />\n\t)\n}\n\nexport default SearchIcon\n"], "names": [], "mappings": ";;;;AAAA;;;AAGA,MAAM,aAAiB;IACtB,qBACC,8OAAC,6HAAA,CAAA,UAAK;QAAC,KAAK;QAAoB,KAAI;QAAS,OAAO;QAAI,QAAQ;;;;;;AAElE;uCAEe", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/shared/ui/input/index.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"input\": \"index-module__Bu1eGa__input\",\n  \"label\": \"index-module__Bu1eGa__label\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/input/index.tsx"], "sourcesContent": ["import { FC, HTMLInputTypeAttribute, ReactNode } from 'react'\nimport style from './index.module.css'\n\ninterface InputProps {\n\ttype?: HTMLInputTypeAttribute\n\tname?: string\n\tvalue?: string\n\tplaceholder?: string\n\tprefix?: ReactNode\n\tonChange?: (event: React.ChangeEvent<HTMLInputElement>) => void\n}\n\nconst Input: FC<InputProps> = ({\n\ttype,\n\tname,\n\tvalue,\n\tplaceholder,\n\tprefix,\n\tonChange\n}) => {\n\treturn (\n\t\t<label className={style.label}>\n\t\t\t{prefix}\n\t\t\t<input\n\t\t\t\tclassName={style.input}\n\t\t\t\ttype={type}\n\t\t\t\tname={name}\n\t\t\t\tvalue={value}\n\t\t\t\tplaceholder={placeholder}\n\t\t\t\tonChange={onChange}\n\t\t\t/>\n\t\t</label>\n\t)\n}\n\nexport default Input\n"], "names": [], "mappings": ";;;;AACA;;;AAWA,MAAM,QAAwB,CAAC,EAC9B,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,WAAW,EACX,MAAM,EACN,QAAQ,EACR;IACA,qBACC,8OAAC;QAAM,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;;YAC3B;0BACD,8OAAC;gBACA,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;gBACtB,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;;;;;;;;;;;;AAId;uCAEe", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/model/searchStore.ts"], "sourcesContent": ["import { create } from 'zustand'\n\ninterface SearchStore {\n\tsearchQuery: string\n\tsetSearchQuery: (value: string) => void\n}\n\nexport const useSearchStore = create<SearchStore>(set => ({\n\tsearchQuery: '',\n\tsetSearchQuery: value => set({ searchQuery: value })\n}))\n"], "names": [], "mappings": ";;;AAAA;;AAOO,MAAM,iBAAiB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAe,CAAA,MAAO,CAAC;QACzD,aAAa;QACb,gBAAgB,CAAA,QAAS,IAAI;gBAAE,aAAa;YAAM;IACnD,CAAC", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/hero/ui/index.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"dropdown\": \"index-module__m10heG__dropdown\",\n  \"hero\": \"index-module__m10heG__hero\",\n  \"notFound\": \"index-module__m10heG__notFound\",\n  \"search\": \"index-module__m10heG__search\",\n  \"title\": \"index-module__m10heG__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/searchDropdown.tsx"], "sourcesContent": ["import { FC } from 'react'\nimport style from './index.module.css'\n\ninterface SearchDropdownProps {\n\tsearchQuery: string\n}\n\nconst SearchDropdown: FC<SearchDropdownProps> = ({ searchQuery }) => {\n\treturn (\n\t\t<div className={style.dropdown} data-is-visible={!!searchQuery.length}>\n\t\t\t<span className={style.notFound}>Ничего не найдено :(</span>\n\t\t</div>\n\t)\n}\n\nexport default SearchDropdown\n"], "names": [], "mappings": ";;;;AACA;;;AAMA,MAAM,iBAA0C,CAAC,EAAE,WAAW,EAAE;IAC/D,qBACC,8OAAC;QAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,QAAQ;QAAE,mBAAiB,CAAC,CAAC,YAAY,MAAM;kBACpE,cAAA,8OAAC;YAAK,WAAW,iJAAA,CAAA,UAAK,CAAC,QAAQ;sBAAE;;;;;;;;;;;AAGpC;uCAEe", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/search.tsx"], "sourcesContent": ["'use client'\n\nimport SearchIcon from '@/src/shared/ui/icons/search'\nimport Input from '@/src/shared/ui/input'\nimport { useSearchStore } from '../model/searchStore'\nimport style from './index.module.css'\nimport SearchDropdown from './searchDropdown'\n\nconst HeroSearch = () => {\n\tconst { searchQuery, setSearchQuery } = useSearchStore()\n\n\tconst handleSearch = (value: string) => {\n\t\tsetSearchQuery(value)\n\t\tconsole.log(value)\n\t}\n\n\treturn (\n\t\t<div className={style.search}>\n\t\t\t<Input\n\t\t\t\tplaceholder='Найти направление'\n\t\t\t\tprefix={<SearchIcon />}\n\t\t\t\tvalue={searchQuery}\n\t\t\t\tonChange={value => handleSearch(value.target.value)}\n\t\t\t/>\n\t\t\t<SearchDropdown searchQuery={searchQuery} />\n\t\t</div>\n\t)\n}\n\nexport default HeroSearch\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,aAAa;IAClB,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,iBAAc,AAAD;IAErD,MAAM,eAAe,CAAC;QACrB,eAAe;QACf,QAAQ,GAAG,CAAC;IACb;IAEA,qBACC,8OAAC;QAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,MAAM;;0BAC3B,8OAAC,sIAAA,CAAA,UAAK;gBACL,aAAY;gBACZ,sBAAQ,8OAAC,uIAAA,CAAA,UAAU;;;;;gBACnB,OAAO;gBACP,UAAU,CAAA,QAAS,aAAa,MAAM,MAAM,CAAC,KAAK;;;;;;0BAEnD,8OAAC,+IAAA,CAAA,UAAc;gBAAC,aAAa;;;;;;;;;;;;AAGhC;uCAEe", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/entities/country/api/index.ts"], "sourcesContent": ["import { CountriesI } from '../model/country'\n\nexport class CountryApi {\n\tasync fetchCountries(): Promise<CountriesI> {\n\t\tconst res = await fetch(\n\t\t\t'https://api3.yesim.cc/sale_list?force_type=countries&lang=ru'\n\t\t)\n\t\tconst data: CountriesI = await res.json()\n\t\treturn data\n\t}\n}\n"], "names": [], "mappings": ";;;AAEO,MAAM;IACZ,MAAM,iBAAsC;QAC3C,MAAM,MAAM,MAAM,MACjB;QAED,MAAM,OAAmB,MAAM,IAAI,IAAI;QACvC,OAAO;IACR;AACD", "debugId": null}}, {"offset": {"line": 207, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/pages/home/<USER>/countries.ts"], "sourcesContent": ["// zustand store\nimport { CountriesI } from '@/src/entities/country/model/country'\nimport { create } from 'zustand'\n\ninterface CountriesStore {\n    countries: CountriesI\n    setCountries: (countries: CountriesI) => void\n}\n\nexport const useCountriesStore = create<CountriesStore>(set => ({\n    countries: { countries: {} },\n    setCountries: countries => set({ countries })\n}))\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;AAEhB;;AAOO,MAAM,oBAAoB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAkB,CAAA,MAAO,CAAC;QAC5D,WAAW;YAAE,WAAW,CAAC;QAAE;QAC3B,cAAc,CAAA,YAAa,IAAI;gBAAE;YAAU;IAC/C,CAAC", "debugId": null}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/hooks/useCountries.ts"], "sourcesContent": ["'use client'\n\nimport { CountryApi } from '@/src/entities/country/api'\nimport { useCountriesStore } from '@/src/pages/home/<USER>/countries'\nimport { useCallback } from 'react'\n\n/**\n * Хук для работы с данными стран\n * Предоставляет методы для загрузки и управления данными стран через Zustand стор\n */\nexport const useCountries = () => {\n\tconst { countries, setCountries } = useCountriesStore()\n\n\t/**\n\t * Загружает данные стран с API и сохраняет в стор\n\t */\n\tconst fetchCountries = useCallback(async () => {\n\t\ttry {\n\t\t\tconst countryApi = new CountryApi()\n\t\t\tconst data = await countryApi.fetchCountries()\n\t\t\tsetCountries(data)\n\t\t\treturn data\n\t\t} catch (error) {\n\t\t\tconsole.error('Ошибка при загрузке стран:', error)\n\t\t\tthrow error\n\t\t}\n\t}, [setCountries])\n\n\t/**\n\t * Получает страны по языку\n\t */\n\tconst getCountriesByLanguage = useCallback((lang: string) => {\n\t\treturn countries.countries?.[lang] || []\n\t}, [countries])\n\n\t/**\n\t * Получает русские страны\n\t */\n\tconst getRussianCountries = useCallback(() => {\n\t\treturn getCountriesByLanguage('ru')\n\t}, [getCountriesByLanguage])\n\n\t/**\n\t * Проверяет, загружены ли данные\n\t */\n\tconst isLoaded = useCallback(() => {\n\t\treturn countries.countries && Object.keys(countries.countries).length > 0\n\t}, [countries])\n\n\treturn {\n\t\tcountries,\n\t\tfetchCountries,\n\t\tgetCountriesByLanguage,\n\t\tgetRussianCountries,\n\t\tisLoaded,\n\t\tsetCountries\n\t}\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AAJA;;;;AAUO,MAAM,eAAe;IAC3B,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,oBAAiB,AAAD;IAEpD;;EAEC,GACD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,IAAI;YACH,MAAM,aAAa,IAAI,0IAAA,CAAA,aAAU;YACjC,MAAM,OAAO,MAAM,WAAW,cAAc;YAC5C,aAAa;YACb,OAAO;QACR,EAAE,OAAO,OAAO;YACf,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACP;IACD,GAAG;QAAC;KAAa;IAEjB;;EAEC,GACD,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC3C,OAAO,UAAU,SAAS,EAAE,CAAC,KAAK,IAAI,EAAE;IACzC,GAAG;QAAC;KAAU;IAEd;;EAEC,GACD,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,OAAO,uBAAuB;IAC/B,GAAG;QAAC;KAAuB;IAE3B;;EAEC,GACD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,OAAO,UAAU,SAAS,IAAI,OAAO,IAAI,CAAC,UAAU,SAAS,EAAE,MAAM,GAAG;IACzE,GAAG;QAAC;KAAU;IAEd,OAAO;QACN;QACA;QACA;QACA;QACA;QACA;IACD;AACD", "debugId": null}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/main/ui/index.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"card\": \"index-module__aQ9zUq__card\",\n  \"loadMore\": \"index-module__aQ9zUq__loadMore\",\n  \"main\": \"index-module__aQ9zUq__main\",\n  \"title\": \"index-module__aQ9zUq__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 295, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/main/ui/countriesStats.tsx"], "sourcesContent": ["'use client'\n\nimport { useCountries } from '@/src/shared/hooks/useCountries'\nimport { FC } from 'react'\nimport style from './index.module.css'\n\n/**\n * Компонент для отображения статистики стран\n * Демонстрирует использование Zustand стора на клиенте\n */\nconst CountriesStats: FC = () => {\n\tconst { countries, getRussianCountries, getCountriesByLanguage, isLoaded } = useCountries()\n\n\tif (!isLoaded()) {\n\t\treturn (\n\t\t\t<div className={style.card}>\n\t\t\t\t<h3 className={style.title}>Статистика стран</h3>\n\t\t\t\t<p>Загрузка данных...</p>\n\t\t\t</div>\n\t\t)\n\t}\n\n\tconst russianCountries = getRussianCountries()\n\tconst allLanguages = Object.keys(countries.countries || {})\n\tconst totalCountries = allLanguages.reduce((total, lang) => {\n\t\treturn total + (getCountriesByLanguage(lang).length || 0)\n\t}, 0)\n\n\treturn (\n\t\t<div className={style.card}>\n\t\t\t<h3 className={style.title}>Статистика стран</h3>\n\t\t\t<div style={{ padding: '1rem' }}>\n\t\t\t\t<p><strong>Всего стран:</strong> {totalCountries}</p>\n\t\t\t\t<p><strong>Русских стран:</strong> {russianCountries.length}</p>\n\t\t\t\t<p><strong>Доступных языков:</strong> {allLanguages.length}</p>\n\t\t\t\t<div style={{ marginTop: '0.5rem' }}>\n\t\t\t\t\t<strong>Языки:</strong> {allLanguages.join(', ')}\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</div>\n\t)\n}\n\nexport default CountriesStats\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAMA;;;CAGC,GACD,MAAM,iBAAqB;IAC1B,MAAM,EAAE,SAAS,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,eAAY,AAAD;IAExF,IAAI,CAAC,YAAY;QAChB,qBACC,8OAAC;YAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,IAAI;;8BACzB,8OAAC;oBAAG,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;8BAAE;;;;;;8BAC5B,8OAAC;8BAAE;;;;;;;;;;;;IAGN;IAEA,MAAM,mBAAmB;IACzB,MAAM,eAAe,OAAO,IAAI,CAAC,UAAU,SAAS,IAAI,CAAC;IACzD,MAAM,iBAAiB,aAAa,MAAM,CAAC,CAAC,OAAO;QAClD,OAAO,QAAQ,CAAC,uBAAuB,MAAM,MAAM,IAAI,CAAC;IACzD,GAAG;IAEH,qBACC,8OAAC;QAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,IAAI;;0BACzB,8OAAC;gBAAG,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;0BAAE;;;;;;0BAC5B,8OAAC;gBAAI,OAAO;oBAAE,SAAS;gBAAO;;kCAC7B,8OAAC;;0CAAE,8OAAC;0CAAO;;;;;;4BAAqB;4BAAE;;;;;;;kCAClC,8OAAC;;0CAAE,8OAAC;0CAAO;;;;;;4BAAuB;4BAAE,iBAAiB,MAAM;;;;;;;kCAC3D,8OAAC;;0CAAE,8OAAC;0CAAO;;;;;;4BAA0B;4BAAE,aAAa,MAAM;;;;;;;kCAC1D,8OAAC;wBAAI,OAAO;4BAAE,WAAW;wBAAS;;0CACjC,8OAAC;0CAAO;;;;;;4BAAe;4BAAE,aAAa,IAAI,CAAC;;;;;;;;;;;;;;;;;;;AAKhD;uCAEe", "debugId": null}}, {"offset": {"line": 445, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/entities/country/ui/index.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n});\n"], "names": [], "mappings": "AAAA;AACA"}}, {"offset": {"line": 451, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/entities/country/ui/card.tsx"], "sourcesContent": ["'use client'\n\nimport { FC } from 'react'\nimport { CountryI } from '../model/country'\nimport style from './index.module.css'\n\ninterface CountryCardProps {\n    country: CountryI\n    onClick?: () => void\n}\n\nconst CountryCard: FC<CountryCardProps> = ({ country, onClick }) => {\n    return (\n        <button onClick={onClick} className={style.card}>\n            <span className={style.title}>{country.country}</span>\n        </button>\n    )\n}\n\nexport default CountryCard\n"], "names": [], "mappings": ";;;;AAIA;AAJA;;;AAWA,MAAM,cAAoC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE;IAC3D,qBACI,8OAAC;QAAO,SAAS;QAAS,WAAW,qJAAA,CAAA,UAAK,CAAC,IAAI;kBAC3C,cAAA,8OAAC;YAAK,WAAW,qJAAA,CAAA,UAAK,CAAC,KAAK;sBAAG,QAAQ,OAAO;;;;;;;;;;;AAG1D;uCAEe", "debugId": null}}, {"offset": {"line": 481, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/shared/ui/buttons/textButton/index.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"button\": \"index-module__mWR7qa__button\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 488, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/buttons/textButton/index.tsx"], "sourcesContent": ["import { FC } from 'react'\nimport style from './index.module.css'\n\ninterface TextButtonProps {\n\ttext: string\n\tonClick?: () => void\n}\n\nconst TextButton: FC<TextButtonProps> = ({ text, onClick }) => {\n\treturn (\n\t\t<button className={style.button} onClick={onClick}>\n\t\t\t{text}\n\t\t</button>\n\t)\n}\n\nexport default TextButton\n"], "names": [], "mappings": ";;;;AACA;;;AAOA,MAAM,aAAkC,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE;IACzD,qBACC,8OAAC;QAAO,WAAW,iKAAA,CAAA,UAAK,CAAC,MAAM;QAAE,SAAS;kBACxC;;;;;;AAGJ;uCAEe", "debugId": null}}, {"offset": {"line": 511, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/main/ui/popularCountries.tsx"], "sourcesContent": ["'use client'\n\nimport { CountriesI, CountryI } from '@/src/entities/country/model/country'\nimport CountryCard from '@/src/entities/country/ui/card'\nimport { useCountries } from '@/src/shared/hooks/useCountries'\nimport TextButton from '@/src/shared/ui/buttons/textButton'\nimport { FC, useEffect } from 'react'\nimport style from './index.module.css'\n\ninterface PopularCountriesProps {\n\tinitialCountries?: CountriesI\n}\n\nconst PopularCountries: FC<PopularCountriesProps> = ({\n\tinitialCountries\n}) => {\n\tconst { setCountries, getRussianCountries, isLoaded } = useCountries()\n\n\t// Устанавливаем начальные данные в стор при первом рендере\n\tuseEffect(() => {\n\t\tif (initialCountries && !isLoaded()) {\n\t\t\tsetCountries(initialCountries)\n\t\t}\n\t}, [initialCountries, isLoaded, setCountries])\n\n\t// Получаем русские страны через хук\n\tconst ruCountries = getRussianCountries()\n\n\tconst showMore = () => {\n\t\tconsole.log('Показать все страны')\n\t\t// Здесь можно добавить логику для показа всех стран\n\t}\n\n\treturn (\n\t\t<div className={`${style.card} ${style.popularCountries}`}>\n\t\t\t<h2 className={style.title}>Популярные страны</h2>\n\t\t\t<div className={style.countries}>\n\t\t\t\t{ruCountries.map((data: CountryI) => (\n\t\t\t\t\t<CountryCard\n\t\t\t\t\t\tkey={data.id}\n\t\t\t\t\t\tcountry={data}\n\t\t\t\t\t/>\n\t\t\t\t))}\n\t\t\t</div>\n\t\t\t<div className={style.loadMore}>\n\t\t\t\t<TextButton text='Показать все страны' onClick={showMore} />\n\t\t\t</div>\n\t\t</div>\n\t)\n}\n\nexport default PopularCountries\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAPA;;;;;;;AAaA,MAAM,mBAA8C,CAAC,EACpD,gBAAgB,EAChB;IACA,MAAM,EAAE,YAAY,EAAE,mBAAmB,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,eAAY,AAAD;IAEnE,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACT,IAAI,oBAAoB,CAAC,YAAY;YACpC,aAAa;QACd;IACD,GAAG;QAAC;QAAkB;QAAU;KAAa;IAE7C,oCAAoC;IACpC,MAAM,cAAc;IAEpB,MAAM,WAAW;QAChB,QAAQ,GAAG,CAAC;IACZ,oDAAoD;IACrD;IAEA,qBACC,8OAAC;QAAI,WAAW,GAAG,iJAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,EAAE,iJAAA,CAAA,UAAK,CAAC,gBAAgB,EAAE;;0BACxD,8OAAC;gBAAG,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;0BAAE;;;;;;0BAC5B,8OAAC;gBAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,SAAS;0BAC7B,YAAY,GAAG,CAAC,CAAC,qBACjB,8OAAC,yIAAA,CAAA,UAAW;wBAEX,SAAS;uBADJ,KAAK,EAAE;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,QAAQ;0BAC7B,cAAA,8OAAC,sJAAA,CAAA,UAAU;oBAAC,MAAK;oBAAsB,SAAS;;;;;;;;;;;;;;;;;AAIpD;uCAEe", "debugId": null}}]}