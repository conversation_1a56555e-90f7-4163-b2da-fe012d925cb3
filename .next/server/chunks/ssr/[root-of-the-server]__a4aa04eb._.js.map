{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/icons/search.tsx"], "sourcesContent": ["import Image from 'next/image'\nimport { FC } from 'react'\n\nconst SearchIcon: FC = () => {\n\treturn (\n\t\t<Image src={'/search-icon.svg'} alt='Search' width={20} height={20} />\n\t)\n}\n\nexport default SearchIcon\n"], "names": [], "mappings": ";;;;AAAA;;;AAGA,MAAM,aAAiB;IACtB,qBACC,8OAAC,6HAAA,CAAA,UAAK;QAAC,KAAK;QAAoB,KAAI;QAAS,OAAO;QAAI,QAAQ;;;;;;AAElE;uCAEe", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/shared/ui/input/index.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"input\": \"index-module__Bu1eGa__input\",\n  \"label\": \"index-module__Bu1eGa__label\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/input/index.tsx"], "sourcesContent": ["import { FC, HTMLInputTypeAttribute, ReactNode } from 'react'\nimport style from './index.module.css'\n\ninterface InputProps {\n\ttype?: HTMLInputTypeAttribute\n\tname?: string\n\tvalue?: string\n\tplaceholder?: string\n\tprefix?: ReactNode\n\tonChange?: (event: React.ChangeEvent<HTMLInputElement>) => void\n}\n\nconst Input: FC<InputProps> = ({\n\ttype,\n\tname,\n\tvalue,\n\tplaceholder,\n\tprefix,\n\tonChange\n}) => {\n\treturn (\n\t\t<label className={style.label}>\n\t\t\t{prefix}\n\t\t\t<input\n\t\t\t\tclassName={style.input}\n\t\t\t\ttype={type}\n\t\t\t\tname={name}\n\t\t\t\tvalue={value}\n\t\t\t\tplaceholder={placeholder}\n\t\t\t\tonChange={onChange}\n\t\t\t/>\n\t\t</label>\n\t)\n}\n\nexport default Input\n"], "names": [], "mappings": ";;;;AACA;;;AAWA,MAAM,QAAwB,CAAC,EAC9B,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,WAAW,EACX,MAAM,EACN,QAAQ,EACR;IACA,qBACC,8OAAC;QAAM,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;;YAC3B;0BACD,8OAAC;gBACA,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;gBACtB,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;;;;;;;;;;;;AAId;uCAEe", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/model/searchStore.ts"], "sourcesContent": ["import { create } from 'zustand'\n\ninterface SearchStore {\n\tsearchQuery: string\n\tsetSearchQuery: (value: string) => void\n}\n\nexport const useSearchStore = create<SearchStore>(set => ({\n\tsearchQuery: '',\n\tsetSearchQuery: value => set({ searchQuery: value })\n}))\n"], "names": [], "mappings": ";;;AAAA;;AAOO,MAAM,iBAAiB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAe,CAAA,MAAO,CAAC;QACzD,aAAa;QACb,gBAAgB,CAAA,QAAS,IAAI;gBAAE,aAAa;YAAM;IACnD,CAAC", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/hero/ui/index.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"dropdown\": \"index-module__m10heG__dropdown\",\n  \"hero\": \"index-module__m10heG__hero\",\n  \"notFound\": \"index-module__m10heG__notFound\",\n  \"search\": \"index-module__m10heG__search\",\n  \"title\": \"index-module__m10heG__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/searchDropdown.tsx"], "sourcesContent": ["import { FC } from 'react'\nimport style from './index.module.css'\n\ninterface SearchDropdownProps {\n\tsearchQuery: string\n}\n\nconst SearchDropdown: FC<SearchDropdownProps> = ({ searchQuery }) => {\n\treturn (\n\t\t<div className={style.dropdown} data-is-visible={!!searchQuery.length}>\n\t\t\t<span className={style.notFound}>Ничего не найдено :(</span>\n\t\t</div>\n\t)\n}\n\nexport default SearchDropdown\n"], "names": [], "mappings": ";;;;AACA;;;AAMA,MAAM,iBAA0C,CAAC,EAAE,WAAW,EAAE;IAC/D,qBACC,8OAAC;QAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,QAAQ;QAAE,mBAAiB,CAAC,CAAC,YAAY,MAAM;kBACpE,cAAA,8OAAC;YAAK,WAAW,iJAAA,CAAA,UAAK,CAAC,QAAQ;sBAAE;;;;;;;;;;;AAGpC;uCAEe", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/search.tsx"], "sourcesContent": ["'use client'\n\nimport SearchIcon from '@/src/shared/ui/icons/search'\nimport Input from '@/src/shared/ui/input'\nimport { useSearchStore } from '../model/searchStore'\nimport style from './index.module.css'\nimport SearchDropdown from './searchDropdown'\n\nconst HeroSearch = () => {\n\tconst { searchQuery, setSearchQuery } = useSearchStore()\n\n\tconst handleSearch = (value: string) => {\n\t\tsetSearchQuery(value)\n\t\tconsole.log(value)\n\t}\n\n\treturn (\n\t\t<div className={style.search}>\n\t\t\t<Input\n\t\t\t\tplaceholder='Найти направление'\n\t\t\t\tprefix={<SearchIcon />}\n\t\t\t\tvalue={searchQuery}\n\t\t\t\tonChange={value => handleSearch(value.target.value)}\n\t\t\t/>\n\t\t\t<SearchDropdown searchQuery={searchQuery} />\n\t\t</div>\n\t)\n}\n\nexport default HeroSearch\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,aAAa;IAClB,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,iBAAc,AAAD;IAErD,MAAM,eAAe,CAAC;QACrB,eAAe;QACf,QAAQ,GAAG,CAAC;IACb;IAEA,qBACC,8OAAC;QAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,MAAM;;0BAC3B,8OAAC,sIAAA,CAAA,UAAK;gBACL,aAAY;gBACZ,sBAAQ,8OAAC,uIAAA,CAAA,UAAU;;;;;gBACnB,OAAO;gBACP,UAAU,CAAA,QAAS,aAAa,MAAM,MAAM,CAAC,KAAK;;;;;;0BAEnD,8OAAC,+IAAA,CAAA,UAAc;gBAAC,aAAa;;;;;;;;;;;;AAGhC;uCAEe", "debugId": null}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/entities/country/ui/index.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n});\n"], "names": [], "mappings": "AAAA;AACA"}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/entities/country/ui/card.tsx"], "sourcesContent": ["'use client'\n\nimport { FC } from 'react'\nimport { CountryI } from '../model/country'\nimport style from './index.module.css'\n\ninterface CountryCardProps {\n    country: CountryI\n    onClick?: () => void\n}\n\nconst CountryCard: FC<CountryCardProps> = ({ country, onClick }) => {\n    return (\n        <button onClick={onClick} className={style.card}>\n            <span className={style.title}>{country.country}</span>\n        </button>\n    )\n}\n\nexport default CountryCard\n"], "names": [], "mappings": ";;;;AAIA;AAJA;;;AAWA,MAAM,cAAoC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE;IAC3D,qBACI,8OAAC;QAAO,SAAS;QAAS,WAAW,qJAAA,CAAA,UAAK,CAAC,IAAI;kBAC3C,cAAA,8OAAC;YAAK,WAAW,qJAAA,CAAA,UAAK,CAAC,KAAK;sBAAG,QAAQ,OAAO;;;;;;;;;;;AAG1D;uCAEe", "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/pages/home/<USER>/countries.ts"], "sourcesContent": ["// zustand store\nimport { CountriesI } from '@/src/entities/country/model/country'\nimport { create } from 'zustand'\n\ninterface CountriesStore {\n    countries: CountriesI\n    setCountries: (countries: CountriesI) => void\n}\n\nexport const useCountriesStore = create<CountriesStore>(set => ({\n    countries: { countries: {} },\n    setCountries: countries => set({ countries })\n}))\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;AAEhB;;AAOO,MAAM,oBAAoB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAkB,CAAA,MAAO,CAAC;QAC5D,WAAW;YAAE,WAAW,CAAC;QAAE;QAC3B,cAAc,CAAA,YAAa,IAAI;gBAAE;YAAU;IAC/C,CAAC", "debugId": null}}, {"offset": {"line": 246, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/shared/ui/buttons/textButton/index.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"button\": \"index-module__mWR7qa__button\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/buttons/textButton/index.tsx"], "sourcesContent": ["import { FC } from 'react'\nimport style from './index.module.css'\n\ninterface TextButtonProps {\n\ttext: string\n\tonClick?: () => void\n}\n\nconst TextButton: FC<TextButtonProps> = ({ text, onClick }) => {\n\treturn (\n\t\t<button className={style.button} onClick={onClick}>\n\t\t\t{text}\n\t\t</button>\n\t)\n}\n\nexport default TextButton\n"], "names": [], "mappings": ";;;;AACA;;;AAOA,MAAM,aAAkC,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE;IACzD,qBACC,8OAAC;QAAO,WAAW,iKAAA,CAAA,UAAK,CAAC,MAAM;QAAE,SAAS;kBACxC;;;;;;AAGJ;uCAEe", "debugId": null}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/main/ui/index.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"card\": \"index-module__aQ9zUq__card\",\n  \"loadMore\": \"index-module__aQ9zUq__loadMore\",\n  \"main\": \"index-module__aQ9zUq__main\",\n  \"title\": \"index-module__aQ9zUq__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/main/ui/popularCountries.tsx"], "sourcesContent": ["'use client'\n\nimport { CountriesI, CountryI } from '@/src/entities/country/model/country'\nimport CountryCard from '@/src/entities/country/ui/card'\nimport { useCountriesStore } from '@/src/pages/home/<USER>/countries'\nimport TextButton from '@/src/shared/ui/buttons/textButton'\nimport { FC, useEffect } from 'react'\nimport style from './index.module.css'\n\ninterface PopularCountriesProps {\n\tinitialCountries?: CountriesI\n}\n\nconst PopularCountries: FC<PopularCountriesProps> = ({\n\tinitialCountries\n}) => {\n\tconst { countries, setCountries } = useCountriesStore()\n\n\t// Устанавливаем начальные данные в стор при первом рендере\n\tuseEffect(() => {\n\t\tif (initialCountries && (!countries.countries || Object.keys(countries.countries).length === 0)) {\n\t\t\tsetCountries(initialCountries)\n\t\t}\n\t}, [initialCountries, countries, setCountries])\n\n\t// Получаем данные из стора\n\tconst countriesData = countries.countries\n\tconst ruCountries: CountryI[] = countriesData?.['ru'] || []\n\n\tconst showMore = () => {\n\t\tconsole.log('Показать все страны')\n\t\t// Здесь можно добавить логику для показа всех стран\n\t}\n\n\treturn (\n\t\t<div className={`${style.card} ${style.popularCountries}`}>\n\t\t\t<h2 className={style.title}>Популярные страны</h2>\n\t\t\t<div className={style.countries}>\n\t\t\t\t{ruCountries.map(data => (\n\t\t\t\t\t<CountryCard\n\t\t\t\t\t\tkey={data.id}\n\t\t\t\t\t\tcountry={data}\n\t\t\t\t\t/>\n\t\t\t\t))}\n\t\t\t</div>\n\t\t\t<div className={style.loadMore}>\n\t\t\t\t<TextButton text='Показать все страны' onClick={showMore} />\n\t\t\t</div>\n\t\t</div>\n\t)\n}\n\nexport default PopularCountries\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAPA;;;;;;;AAaA,MAAM,mBAA8C,CAAC,EACpD,gBAAgB,EAChB;IACA,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,oBAAiB,AAAD;IAEpD,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACT,IAAI,oBAAoB,CAAC,CAAC,UAAU,SAAS,IAAI,OAAO,IAAI,CAAC,UAAU,SAAS,EAAE,MAAM,KAAK,CAAC,GAAG;YAChG,aAAa;QACd;IACD,GAAG;QAAC;QAAkB;QAAW;KAAa;IAE9C,2BAA2B;IAC3B,MAAM,gBAAgB,UAAU,SAAS;IACzC,MAAM,cAA0B,eAAe,CAAC,KAAK,IAAI,EAAE;IAE3D,MAAM,WAAW;QAChB,QAAQ,GAAG,CAAC;IACZ,oDAAoD;IACrD;IAEA,qBACC,8OAAC;QAAI,WAAW,GAAG,iJAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,EAAE,iJAAA,CAAA,UAAK,CAAC,gBAAgB,EAAE;;0BACxD,8OAAC;gBAAG,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;0BAAE;;;;;;0BAC5B,8OAAC;gBAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,SAAS;0BAC7B,YAAY,GAAG,CAAC,CAAA,qBAChB,8OAAC,yIAAA,CAAA,UAAW;wBAEX,SAAS;uBADJ,KAAK,EAAE;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,QAAQ;0BAC7B,cAAA,8OAAC,sJAAA,CAAA,UAAU;oBAAC,MAAK;oBAAsB,SAAS;;;;;;;;;;;;;;;;;AAIpD;uCAEe", "debugId": null}}]}