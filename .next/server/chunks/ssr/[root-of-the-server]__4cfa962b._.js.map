{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/shared/ui/logo/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"logo\": \"index-module__ytjyqG__logo\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/logo/index.tsx"], "sourcesContent": ["import Image from 'next/image';\nimport { FC } from 'react';\nimport style from './index.module.css';\n\ninterface LogoProps {\n    withType?: boolean;\n}\n\nconst Logo: FC<LogoProps> = ({ withType = true }) => {\n    return (\n        <div className={style.logo}>\n            <Image src={'/yesim_circle.svg'} alt='Logo' width={30} height={30} />\n            {withType && <Image src={'/yesim_type.svg'} alt='Logo' width={65} height={23} />}\n        </div>\n    );\n}\n\nexport default Logo;"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAMA,MAAM,OAAsB,CAAC,EAAE,WAAW,IAAI,EAAE;IAC5C,qBACI,8OAAC;QAAI,WAAW,gJAAA,CAAA,UAAK,CAAC,IAAI;;0BACtB,8OAAC,6HAAA,CAAA,UAAK;gBAAC,KAAK;gBAAqB,KAAI;gBAAO,OAAO;gBAAI,QAAQ;;;;;;YAC9D,0BAAY,8OAAC,6HAAA,CAAA,UAAK;gBAAC,KAAK;gBAAmB,KAAI;gBAAO,OAAO;gBAAI,QAAQ;;;;;;;;;;;;AAGtF;uCAEe", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/header/ui/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"header\": \"index-module__6gvmdq__header\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/header/ui/index.tsx"], "sourcesContent": ["import Logo from '@/src/shared/ui/logo'\nimport { FC } from 'react'\nimport style from './index.module.css'\n\nconst Header: FC = () => {\n    return <header className={style.header}>\n        <Logo />\n    </header>\n}\n\nexport default Header\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,SAAa;IACf,qBAAO,8OAAC;QAAO,WAAW,mJAAA,CAAA,UAAK,CAAC,MAAM;kBAClC,cAAA,8OAAC,qIAAA,CAAA,UAAI;;;;;;;;;;AAEb;uCAEe", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/pages/home/<USER>/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"page\": \"index-module__vhjfEW__page\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/pages/home/<USER>/index.tsx"], "sourcesContent": ["import Header from '@/src/widgets/header/ui';\nimport { FC } from 'react';\nimport style from './index.module.css';\n\nconst HomePage: FC = () => {\n    return (\n        <div className={style.page}>\n            <Header />\n\n        </div>\n    );\n}\n\nexport default HomePage;"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,WAAe;IACjB,qBACI,8OAAC;QAAI,WAAW,+IAAA,CAAA,UAAK,CAAC,IAAI;kBACtB,cAAA,8OAAC,wIAAA,CAAA,UAAM;;;;;;;;;;AAInB;uCAEe", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/app/page.tsx"], "sourcesContent": ["import HomePage from '@/src/pages/home/<USER>'\n\nexport default function Home() {\n\treturn <HomePage />\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACvB,qBAAO,8OAAC,oIAAA,CAAA,UAAQ;;;;;AACjB", "debugId": null}}]}