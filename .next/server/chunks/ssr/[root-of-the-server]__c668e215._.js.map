{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/shared/ui/logo/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"logo\": \"index-module__ytjyqG__logo\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/logo/index.tsx"], "sourcesContent": ["import Image from 'next/image'\nimport { FC } from 'react'\nimport style from './index.module.css'\n\ninterface LogoProps {\n\twithType?: boolean\n}\n\nconst Logo: FC<LogoProps> = ({ withType = true }) => {\n\treturn (\n\t\t<div className={style.logo}>\n\t\t\t<Image\n\t\t\t\tsrc={'/yesim_circle.svg'}\n\t\t\t\talt='Logo'\n\t\t\t\twidth={30}\n\t\t\t\theight={30}\n\t\t\t/>\n\t\t\t{withType && (\n\t\t\t\t<Image\n\t\t\t\t\tsrc={'/yesim_type.svg'}\n\t\t\t\t\talt='Logo'\n\t\t\t\t\twidth={65}\n\t\t\t\t\theight={23}\n\t\t\t\t/>\n\t\t\t)}\n\t\t</div>\n\t)\n}\n\nexport default Logo\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAMA,MAAM,OAAsB,CAAC,EAAE,WAAW,IAAI,EAAE;IAC/C,qBACC,8OAAC;QAAI,WAAW,gJAAA,CAAA,UAAK,CAAC,IAAI;;0BACzB,8OAAC,6HAAA,CAAA,UAAK;gBACL,KAAK;gBACL,KAAI;gBACJ,OAAO;gBACP,QAAQ;;;;;;YAER,0BACA,8OAAC,6HAAA,CAAA,UAAK;gBACL,KAAK;gBACL,KAAI;gBACJ,OAAO;gBACP,QAAQ;;;;;;;;;;;;AAKb;uCAEe", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/header/ui/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"header\": \"index-module__6gvmdq__header\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/header/ui/index.tsx"], "sourcesContent": ["import Logo from '@/src/shared/ui/logo'\nimport { FC } from 'react'\nimport style from './index.module.css'\n\nconst Header: FC = () => {\n\treturn (\n\t\t<header className={style.header}>\n\t\t\t<Logo />\n\t\t</header>\n\t)\n}\n\nexport default Header\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,SAAa;IAClB,qBACC,8OAAC;QAAO,WAAW,mJAAA,CAAA,UAAK,CAAC,MAAM;kBAC9B,cAAA,8OAAC,qIAAA,CAAA,UAAI;;;;;;;;;;AAGR;uCAEe", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/hero/ui/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"dropdown\": \"index-module__m10heG__dropdown\",\n  \"hero\": \"index-module__m10heG__hero\",\n  \"notFound\": \"index-module__m10heG__notFound\",\n  \"search\": \"index-module__m10heG__search\",\n  \"title\": \"index-module__m10heG__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/search.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/widgets/hero/ui/search.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/widgets/hero/ui/search.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/search.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/widgets/hero/ui/search.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/widgets/hero/ui/search.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/index.tsx"], "sourcesContent": ["import { FC } from 'react'\nimport style from './index.module.css'\nimport HeroSearch from './search'\n\nconst Hero: FC = () => {\n\treturn (\n\t\t<div className={style.hero}>\n\t\t\t<h1 className={style.title}>\n\t\t\t\teSIM карты с интернетом для путешествий и бизнеса\n\t\t\t</h1>\n\t\t\t<HeroSearch />\n\t\t</div>\n\t)\n}\n\nexport default Hero\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEA,MAAM,OAAW;IAChB,qBACC,8OAAC;QAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,IAAI;;0BACzB,8OAAC;gBAAG,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;0BAAE;;;;;;0BAG5B,8OAAC,uIAAA,CAAA,UAAU;;;;;;;;;;;AAGd;uCAEe", "debugId": null}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/howItWorks/ui/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"card\": \"index-module__aAZmyW__card\",\n  \"title\": \"index-module__aAZmyW__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/howItWorks/ui/index.tsx"], "sourcesContent": ["import { FC } from 'react'\nimport style from './index.module.css'\n\nconst HowItsWork: FC = () => {\n\treturn (\n\t\t<div className={style.card}>\n\t\t\t<h2 className={style.title}>Как это работает</h2>\n\t\t</div>\n\t)\n}\n\nexport default HowItsWork\n"], "names": [], "mappings": ";;;;AACA;;;AAEA,MAAM,aAAiB;IACtB,qBACC,8OAAC;QAAI,WAAW,uJAAA,CAAA,UAAK,CAAC,IAAI;kBACzB,cAAA,8OAAC;YAAG,WAAW,uJAAA,CAAA,UAAK,CAAC,KAAK;sBAAE;;;;;;;;;;;AAG/B;uCAEe", "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/main/ui/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"card\": \"index-module__aQ9zUq__card\",\n  \"loadMore\": \"index-module__aQ9zUq__loadMore\",\n  \"main\": \"index-module__aQ9zUq__main\",\n  \"title\": \"index-module__aQ9zUq__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/main/ui/popularCountries.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/widgets/main/ui/popularCountries.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/widgets/main/ui/popularCountries.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4S,GACzU,0EACA", "debugId": null}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/main/ui/popularCountries.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/widgets/main/ui/popularCountries.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/widgets/main/ui/popularCountries.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwR,GACrT,sDACA", "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/main/ui/index.tsx"], "sourcesContent": ["import { FC } from 'react'\nimport HowItsWork from '../../howItWorks/ui'\nimport style from './index.module.css'\nimport PopularCountries from './popularCountries'\n\nconst MainContent: FC = () => {\n    return (\n        <main className={style.main}>\n            <PopularCountries />\n            <HowItsWork />\n        </main>\n    )\n}\n\nexport default MainContent\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAEA,MAAM,cAAkB;IACpB,qBACI,8OAAC;QAAK,WAAW,iJAAA,CAAA,UAAK,CAAC,IAAI;;0BACvB,8OAAC,iJAAA,CAAA,UAAgB;;;;;0BACjB,8OAAC,4IAAA,CAAA,UAAU;;;;;;;;;;;AAGvB;uCAEe", "debugId": null}}, {"offset": {"line": 296, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/pages/home/<USER>/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"content\": \"index-module__vhjfEW__content\",\n  \"page\": \"index-module__vhjfEW__page\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/pages/home/<USER>/index.tsx"], "sourcesContent": ["import Header from '@/src/widgets/header/ui'\nimport Hero from '@/src/widgets/hero/ui'\nimport MainContent from '@/src/widgets/main/ui'\nimport { FC } from 'react'\nimport style from './index.module.css'\n\nconst HomePage: FC = () => {\n\treturn (\n\t\t<div className={style.page}>\n\t\t\t<Header />\n\t\t\t<div className={style.content}>\n\t\t\t\t<Hero />\n\t\t\t\t<MainContent />\n\t\t\t</div>\n\t\t</div>\n\t)\n}\n\nexport default HomePage\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,WAAe;IACpB,qBACC,8OAAC;QAAI,WAAW,+IAAA,CAAA,UAAK,CAAC,IAAI;;0BACzB,8OAAC,wIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAI,WAAW,+IAAA,CAAA,UAAK,CAAC,OAAO;;kCAC5B,8OAAC,sIAAA,CAAA,UAAI;;;;;kCACL,8OAAC,sIAAA,CAAA,UAAW;;;;;;;;;;;;;;;;;AAIhB;uCAEe", "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/app/page.tsx"], "sourcesContent": ["import HomePage from '@/src/pages/home/<USER>'\n\nexport default function Home() {\n\treturn <HomePage />\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACvB,qBAAO,8OAAC,oIAAA,CAAA,UAAQ;;;;;AACjB", "debugId": null}}]}