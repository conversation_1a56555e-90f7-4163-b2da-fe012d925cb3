{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/icons/search.tsx"], "sourcesContent": ["import Image from 'next/image'\nimport { FC } from 'react'\n\nconst SearchIcon: FC = () => {\n\treturn (\n\t\t<Image src={'/search-icon.svg'} alt='Search' width={20} height={20} />\n\t)\n}\n\nexport default SearchIcon\n"], "names": [], "mappings": ";;;;AAAA;;;AAGA,MAAM,aAAiB;IACtB,qBACC,8OAAC,6HAAA,CAAA,UAAK;QAAC,KAAK;QAAoB,KAAI;QAAS,OAAO;QAAI,QAAQ;;;;;;AAElE;uCAEe", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/shared/ui/input/index.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"input\": \"index-module__Bu1eGa__input\",\n  \"label\": \"index-module__Bu1eGa__label\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/input/index.tsx"], "sourcesContent": ["import { FC, HTMLInputTypeAttribute, ReactNode } from 'react'\nimport style from './index.module.css'\n\ninterface InputProps {\n\ttype?: HTMLInputTypeAttribute\n\tname?: string\n\tvalue?: string\n\tplaceholder?: string\n\tprefix?: ReactNode\n\tonChange?: (event: React.ChangeEvent<HTMLInputElement>) => void\n}\n\nconst Input: FC<InputProps> = ({\n\ttype,\n\tname,\n\tvalue,\n\tplaceholder,\n\tprefix,\n\tonChange\n}) => {\n\treturn (\n\t\t<label className={style.label}>\n\t\t\t{prefix}\n\t\t\t<input\n\t\t\t\tclassName={style.input}\n\t\t\t\ttype={type}\n\t\t\t\tname={name}\n\t\t\t\tvalue={value}\n\t\t\t\tplaceholder={placeholder}\n\t\t\t\tonChange={onChange}\n\t\t\t/>\n\t\t</label>\n\t)\n}\n\nexport default Input\n"], "names": [], "mappings": ";;;;AACA;;;AAWA,MAAM,QAAwB,CAAC,EAC9B,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,WAAW,EACX,MAAM,EACN,QAAQ,EACR;IACA,qBACC,8OAAC;QAAM,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;;YAC3B;0BACD,8OAAC;gBACA,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;gBACtB,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;;;;;;;;;;;;AAId;uCAEe", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/model/searchStore.ts"], "sourcesContent": ["\nimport { create } from 'zustand';\n\ninterface SearchStore {\n    searchQuery: string;\n    setSearchQuery: (value: string) => void;\n}\n\nexport const useSearchStore = create<SearchStore>((set) => ({\n    searchQuery: '',\n    setSearchQuery: (value) => set({ searchQuery: value }),\n}))\n"], "names": [], "mappings": ";;;AACA;;AAOO,MAAM,iBAAiB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAe,CAAC,MAAQ,CAAC;QACxD,aAAa;QACb,gBAAgB,CAAC,QAAU,IAAI;gBAAE,aAAa;YAAM;IACxD,CAAC", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/hero/ui/index.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"dropdown\": \"index-module__m10heG__dropdown\",\n  \"hero\": \"index-module__m10heG__hero\",\n  \"notFound\": \"index-module__m10heG__notFound\",\n  \"search\": \"index-module__m10heG__search\",\n  \"title\": \"index-module__m10heG__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/searchDropdown.tsx"], "sourcesContent": ["import { FC } from 'react';\nimport style from './index.module.css';\n\ninterface SearchDropdownProps {\n    searchQuery: string;\n}\n\nconst SearchDropdown: FC<SearchDropdownProps> = ({\n    searchQuery,\n}) => {\n    return (\n        <div className={style.dropdown} data-is-visible={!!searchQuery.length}>\n            <span className={style.notFound}>\n                Ничего не найдено :(\n            </span>\n        </div>\n    );\n}\n\nexport default SearchDropdown;"], "names": [], "mappings": ";;;;AACA;;;AAMA,MAAM,iBAA0C,CAAC,EAC7C,WAAW,EACd;IACG,qBACI,8OAAC;QAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,QAAQ;QAAE,mBAAiB,CAAC,CAAC,YAAY,MAAM;kBACjE,cAAA,8OAAC;YAAK,WAAW,iJAAA,CAAA,UAAK,CAAC,QAAQ;sBAAE;;;;;;;;;;;AAK7C;uCAEe", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/search.tsx"], "sourcesContent": ["'use client'\n\nimport SearchIcon from \"@/src/shared/ui/icons/search\"\nimport Input from \"@/src/shared/ui/input\"\nimport { useSearchStore } from \"../model/searchStore\"\nimport style from './index.module.css'\nimport SearchDropdown from \"./searchDropdown\"\n\nconst HeroSearch = () => {\n    const { searchQuery, setSearchQuery } = useSearchStore()\n\n    const handleSearch = (value: string) => {\n        setSearchQuery(value)\n        console.log(value)\n    }\n\n    return (\n        <div className={style.search}>\n            <Input\n                placeholder='Найти направление'\n                prefix={<SearchIcon />}\n                value={searchQuery}\n                onChange={(value) => handleSearch(value.target.value)}\n            />\n            <SearchDropdown searchQuery={searchQuery} />\n        </div>\n    )\n}\n\nexport default HeroSearch;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,aAAa;IACf,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,iBAAc,AAAD;IAErD,MAAM,eAAe,CAAC;QAClB,eAAe;QACf,QAAQ,GAAG,CAAC;IAChB;IAEA,qBACI,8OAAC;QAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,MAAM;;0BACxB,8OAAC,sIAAA,CAAA,UAAK;gBACF,aAAY;gBACZ,sBAAQ,8OAAC,uIAAA,CAAA,UAAU;;;;;gBACnB,OAAO;gBACP,UAAU,CAAC,QAAU,aAAa,MAAM,MAAM,CAAC,KAAK;;;;;;0BAExD,8OAAC,+IAAA,CAAA,UAAc;gBAAC,aAAa;;;;;;;;;;;;AAGzC;uCAEe", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/pages/home/<USER>/countries.ts"], "sourcesContent": ["// zustand store\nimport { CountriesI } from '@/src/entities/country/model/country';\nimport { create } from 'zustand';\n\ninterface CountriesStore {\n    countries: CountriesI;\n    setCountries: (countries: CountriesI) => void;\n}\n\nexport const useCountriesStore = create<CountriesStore>((set) => ({\n    countries: [],\n    setCountries: (countries) => set({ countries }),\n}))\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;AAEhB;;AAOO,MAAM,oBAAoB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAkB,CAAC,MAAQ,CAAC;QAC9D,WAAW,EAAE;QACb,cAAc,CAAC,YAAc,IAAI;gBAAE;YAAU;IACjD,CAAC", "debugId": null}}, {"offset": {"line": 208, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/howItWorks/ui/index.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"card\": \"index-module__aAZmyW__card\",\n  \"title\": \"index-module__aAZmyW__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/howItWorks/ui/index.tsx"], "sourcesContent": ["import { FC } from 'react'\nimport style from './index.module.css'\n\nconst HowItsWork: FC = () => {\n    return (\n        <div className={style.card}>\n            <h2 className={style.title}>Как это работает</h2>\n        </div>\n    )\n}\n\nexport default HowItsWork\n"], "names": [], "mappings": ";;;;AACA;;;AAEA,MAAM,aAAiB;IACnB,qBACI,8OAAC;QAAI,WAAW,uJAAA,CAAA,UAAK,CAAC,IAAI;kBACtB,cAAA,8OAAC;YAAG,WAAW,uJAAA,CAAA,UAAK,CAAC,KAAK;sBAAE;;;;;;;;;;;AAGxC;uCAEe", "debugId": null}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/main/ui/index.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"card\": \"index-module__aQ9zUq__card\",\n  \"loadMore\": \"index-module__aQ9zUq__loadMore\",\n  \"main\": \"index-module__aQ9zUq__main\",\n  \"title\": \"index-module__aQ9zUq__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/entities/country/api/index.ts"], "sourcesContent": ["import { CountriesI } from '../model/country';\n\nexport class CountryApi {\n    async fetchCountries(): Promise<CountriesI> {\n        const res = await fetch('https://api3.yesim.cc/sale_list?force_type=countries&lang=ru')\n        const data: CountriesI = await res.json()\n        return data\n    }\n}"], "names": [], "mappings": ";;;AAEO,MAAM;IACT,MAAM,iBAAsC;QACxC,MAAM,MAAM,MAAM,MAAM;QACxB,MAAM,OAAmB,MAAM,IAAI,IAAI;QACvC,OAAO;IACX;AACJ", "debugId": null}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/shared/ui/buttons/textButton/index.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"button\": \"index-module__mWR7qa__button\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/buttons/textButton/index.tsx"], "sourcesContent": ["import { FC } from 'react'\nimport style from './index.module.css'\n\ninterface TextButtonProps {\n\ttext: string\n\tonClick?: () => void\n}\n\nconst TextButton: FC<TextButtonProps> = ({ text, onClick }) => {\n\treturn (\n\t\t<button className={style.button} onClick={onClick}>\n\t\t\t{text}\n\t\t</button>\n\t)\n}\n\nexport default TextButton\n"], "names": [], "mappings": ";;;;AACA;;;AAOA,MAAM,aAAkC,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE;IACzD,qBACC,8OAAC;QAAO,WAAW,iKAAA,CAAA,UAAK,CAAC,MAAM;QAAE,SAAS;kBACxC;;;;;;AAGJ;uCAEe", "debugId": null}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/main/ui/popularCountries.tsx"], "sourcesContent": ["import { CountryApi } from '@/src/entities/country/api';\nimport { CountriesI, CountryI } from '@/src/entities/country/model/country';\nimport TextButton from '@/src/shared/ui/buttons/textButton';\nimport { FC } from 'react';\nimport style from './index.module.css';\n\ninterface PopularCountriesProps {\n\tpropsCountries?: CountriesI;\n\tshowMore: () => void;\n}\n\nconst PopularCountries: FC<PopularCountriesProps> = async ({\n\tpropsCountries,\n\tshowMore,\n}) => {\n\t// for seo\n\tconst countryApi = new CountryApi()\n\tconst countries = await countryApi.fetchCountries()\n\n\t// Get the countries data - now using proper Record type\n\tconst countriesData = (propsCountries ?? countries).countries;\n\tconst ruCountries: CountryI[] = countriesData?.['ru'] || [];\n\n\treturn (\n\t\t<div className={`${style.card} ${style.popularCountries}`}>\n\t\t\t<h2 className={style.title}>Популярные страны</h2>\n\t\t\t<div className={style.countries}>\n\t\t\t\t{ruCountries.map((data) => (\n\t\t\t\t\t<div key={data.id} className={style.countryItem}>\n\t\t\t\t\t\t{/* TODO: flag */}\n\t\t\t\t\t\t<div className={style.info}>\n\t\t\t\t\t\t\t<span className={style.countryName}>\n\t\t\t\t\t\t\t\t{data.country}\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t<span className={style.price}>\n\t\t\t\t\t\t\t\tот ₽{data.classic_info?.price_per_gb}/GB\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t))}\n\t\t\t</div>\n\t\t\t<div className={style.loadMore}>\n\t\t\t\t<TextButton text='Показать все страны' onClick={showMore} />\n\t\t\t</div>\n\t\t</div>\n\t)\n}\n\nexport default PopularCountries\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AAEA;;;;;AAOA,MAAM,mBAA8C,OAAO,EAC1D,cAAc,EACd,QAAQ,EACR;IACA,UAAU;IACV,MAAM,aAAa,IAAI,0IAAA,CAAA,aAAU;IACjC,MAAM,YAAY,MAAM,WAAW,cAAc;IAEjD,wDAAwD;IACxD,MAAM,gBAAgB,CAAC,kBAAkB,SAAS,EAAE,SAAS;IAC7D,MAAM,cAA0B,eAAe,CAAC,KAAK,IAAI,EAAE;IAE3D,qBACC,8OAAC;QAAI,WAAW,GAAG,iJAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,EAAE,iJAAA,CAAA,UAAK,CAAC,gBAAgB,EAAE;;0BACxD,8OAAC;gBAAG,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;0BAAE;;;;;;0BAC5B,8OAAC;gBAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,SAAS;0BAC7B,YAAY,GAAG,CAAC,CAAC,qBACjB,8OAAC;wBAAkB,WAAW,iJAAA,CAAA,UAAK,CAAC,WAAW;kCAE9C,cAAA,8OAAC;4BAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,IAAI;;8CACzB,8OAAC;oCAAK,WAAW,iJAAA,CAAA,UAAK,CAAC,WAAW;8CAChC,KAAK,OAAO;;;;;;8CAEd,8OAAC;oCAAK,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;;wCAAE;wCACxB,KAAK,YAAY,EAAE;wCAAa;;;;;;;;;;;;;uBAP9B,KAAK,EAAE;;;;;;;;;;0BAanB,8OAAC;gBAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,QAAQ;0BAC7B,cAAA,8OAAC,sJAAA,CAAA,UAAU;oBAAC,MAAK;oBAAsB,SAAS;;;;;;;;;;;;;;;;;AAIpD;uCAEe", "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/main/ui/index.tsx"], "sourcesContent": ["'use client'\n\nimport { useCountriesStore } from '@/src/pages/home/<USER>/countries'\nimport { FC, useEffect } from 'react'\nimport HowItsWork from '../../howItWorks/ui'\nimport style from './index.module.css'\nimport PopularCountries from './popularCountries'\n\nconst MainContent: FC = async () => {\n    const { countries, setCountries } = useCountriesStore()\n\n    const showMore = () => {\n        console.log('show more')\n    }\n\n    useEffect(() => {\n\n    }, [])\n\n    return (\n        <main className={style.main}>\n            <PopularCountries propsCountries={countries} showMore={showMore} />\n            <HowItsWork />\n        </main>\n    )\n}\n\nexport default MainContent\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,cAAkB;IACpB,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,oBAAiB,AAAD;IAEpD,MAAM,WAAW;QACb,QAAQ,GAAG,CAAC;IAChB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE,KAEV,GAAG,EAAE;IAEL,qBACI,8OAAC;QAAK,WAAW,iJAAA,CAAA,UAAK,CAAC,IAAI;;0BACvB,8OAAC,iJAAA,CAAA,UAAgB;gBAAC,gBAAgB;gBAAW,UAAU;;;;;;0BACvD,8OAAC,4IAAA,CAAA,UAAU;;;;;;;;;;;AAGvB;uCAEe", "debugId": null}}]}