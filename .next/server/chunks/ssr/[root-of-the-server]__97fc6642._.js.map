{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/shared/ui/logo/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"logo\": \"index-module__ytjyqG__logo\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/logo/index.tsx"], "sourcesContent": ["import Image from 'next/image'\nimport { FC } from 'react'\nimport style from './index.module.css'\n\ninterface LogoProps {\n\twithType?: boolean\n}\n\nconst Logo: FC<LogoProps> = ({ withType = true }) => {\n\treturn (\n\t\t<div className={style.logo}>\n\t\t\t<Image\n\t\t\t\tsrc={'/yesim_circle.svg'}\n\t\t\t\talt='Logo'\n\t\t\t\twidth={30}\n\t\t\t\theight={30}\n\t\t\t/>\n\t\t\t{withType && (\n\t\t\t\t<Image\n\t\t\t\t\tsrc={'/yesim_type.svg'}\n\t\t\t\t\talt='Logo'\n\t\t\t\t\twidth={65}\n\t\t\t\t\theight={23}\n\t\t\t\t/>\n\t\t\t)}\n\t\t</div>\n\t)\n}\n\nexport default Logo\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAMA,MAAM,OAAsB,CAAC,EAAE,WAAW,IAAI,EAAE;IAC/C,qBACC,8OAAC;QAAI,WAAW,gJAAA,CAAA,UAAK,CAAC,IAAI;;0BACzB,8OAAC,6HAAA,CAAA,UAAK;gBACL,KAAK;gBACL,KAAI;gBACJ,OAAO;gBACP,QAAQ;;;;;;YAER,0BACA,8OAAC,6HAAA,CAAA,UAAK;gBACL,KAAK;gBACL,KAAI;gBACJ,OAAO;gBACP,QAAQ;;;;;;;;;;;;AAKb;uCAEe", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/header/ui/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"header\": \"index-module__6gvmdq__header\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/header/ui/index.tsx"], "sourcesContent": ["import Logo from '@/src/shared/ui/logo'\nimport { FC } from 'react'\nimport style from './index.module.css'\n\nconst Header: FC = () => {\n\treturn (\n\t\t<header className={style.header}>\n\t\t\t<Logo />\n\t\t</header>\n\t)\n}\n\nexport default Header\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,SAAa;IAClB,qBACC,8OAAC;QAAO,WAAW,mJAAA,CAAA,UAAK,CAAC,MAAM;kBAC9B,cAAA,8OAAC,qIAAA,CAAA,UAAI;;;;;;;;;;AAGR;uCAEe", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/hero/ui/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"dropdown\": \"index-module__m10heG__dropdown\",\n  \"hero\": \"index-module__m10heG__hero\",\n  \"notFound\": \"index-module__m10heG__notFound\",\n  \"search\": \"index-module__m10heG__search\",\n  \"title\": \"index-module__m10heG__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/search.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/widgets/hero/ui/search.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/widgets/hero/ui/search.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/search.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/widgets/hero/ui/search.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/widgets/hero/ui/search.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/index.tsx"], "sourcesContent": ["import { FC } from 'react'\nimport style from './index.module.css'\nimport HeroSearch from './search'\n\nconst Hero: FC = () => {\n    return (\n        <div className={style.hero}>\n            <h1 className={style.title}>\n                eSIM карты с интернетом для путешествий и бизнеса\n            </h1>\n            <HeroSearch />\n        </div>\n    )\n}\n\nexport default Hero\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEA,MAAM,OAAW;IACb,qBACI,8OAAC;QAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,IAAI;;0BACtB,8OAAC;gBAAG,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;0BAAE;;;;;;0BAG5B,8OAAC,uIAAA,CAAA,UAAU;;;;;;;;;;;AAGvB;uCAEe", "debugId": null}}, {"offset": {"line": 183, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/entities/country/api/index.ts"], "sourcesContent": ["import { CountriesI } from '../model/country';\n\nexport class CountryApi {\n    async fetchCountries(): Promise<CountriesI> {\n        const res = await fetch('https://api3.yesim.cc/sale_list?force_type=countries&lang=ru')\n        const data: CountriesI = await res.json()\n        return data\n    }\n}"], "names": [], "mappings": ";;;AAEO,MAAM;IACT,MAAM,iBAAsC;QACxC,MAAM,MAAM,MAAM,MAAM;QACxB,MAAM,OAAmB,MAAM,IAAI,IAAI;QACvC,OAAO;IACX;AACJ", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/howItWorks/ui/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"card\": \"index-module__aAZmyW__card\",\n  \"title\": \"index-module__aAZmyW__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/howItWorks/ui/index.tsx"], "sourcesContent": ["import { FC } from 'react'\nimport style from './index.module.css'\n\nconst HowItsWork: FC = () => {\n    return (\n        <div className={style.card}>\n            <h2 className={style.title}>Как это работает</h2>\n        </div>\n    )\n}\n\nexport default HowItsWork\n"], "names": [], "mappings": ";;;;AACA;;;AAEA,MAAM,aAAiB;IACnB,qBACI,8OAAC;QAAI,WAAW,uJAAA,CAAA,UAAK,CAAC,IAAI;kBACtB,cAAA,8OAAC;YAAG,WAAW,uJAAA,CAAA,UAAK,CAAC,KAAK;sBAAE;;;;;;;;;;;AAGxC;uCAEe", "debugId": null}}, {"offset": {"line": 232, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/main/ui/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"card\": \"index-module__aQ9zUq__card\",\n  \"loadMore\": \"index-module__aQ9zUq__loadMore\",\n  \"main\": \"index-module__aQ9zUq__main\",\n  \"title\": \"index-module__aQ9zUq__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/main/ui/showMoreButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/widgets/main/ui/showMoreButton.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/widgets/main/ui/showMoreButton.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0S,GACvU,wEACA", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/main/ui/showMoreButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/widgets/main/ui/showMoreButton.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/widgets/main/ui/showMoreButton.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsR,GACnT,oDACA", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/main/ui/popularCountries.tsx"], "sourcesContent": ["import { CountryApi } from '@/src/entities/country/api';\nimport { CountriesI, CountryI } from '@/src/entities/country/model/country';\nimport { FC } from 'react';\nimport style from './index.module.css';\nimport ShowMoreButton from './showMoreButton';\n\ninterface PopularCountriesProps {\n\tpropsCountries?: CountriesI;\n}\n\nconst PopularCountries: FC<PopularCountriesProps> = async ({\n\tpropsCountries,\n}) => {\n\t// for seo - получаем данные только если не переданы через пропсы\n\tlet countries = propsCountries;\n\tif (!countries) {\n\t\tconst countryApi = new CountryApi()\n\t\tcountries = await countryApi.fetchCountries()\n\t}\n\n\tconst countriesData = countries.countries;\n\tconst ruCountries: CountryI[] = countriesData?.values().next().value || [];\n\n\tconst handleShowMore = () => {\n\t\t// Логика показа всех стран\n\t\tconsole.log('Показать все страны')\n\t}\n\n\treturn (\n\t\t<div className={`${style.card} ${style.popularCountries}`}>\n\t\t\t<h2 className={style.title}>Популярные страны</h2>\n\t\t\t<div className={style.countries}>\n\t\t\t\t{ruCountries.map((data) => (\n\t\t\t\t\t<div key={data.id} className={style.countryItem}>\n\t\t\t\t\t\t{/* TODO: flag */}\n\t\t\t\t\t\t<div className={style.info}>\n\t\t\t\t\t\t\t<span className={style.countryName}>\n\t\t\t\t\t\t\t\t{data.country}\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t<span className={style.price}>\n\t\t\t\t\t\t\t\tот ₽{data.classic_info?.price_per_gb}/GB\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t))}\n\t\t\t</div>\n\t\t\t<div className={style.loadMore}>\n\t\t\t\t<ShowMoreButton onShowMore={handleShowMore} />\n\t\t\t</div>\n\t\t</div>\n\t)\n}\n\nexport default PopularCountries\n"], "names": [], "mappings": ";;;;AAAA;AAGA;AACA;;;;;AAMA,MAAM,mBAA8C,OAAO,EAC1D,cAAc,EACd;IACA,iEAAiE;IACjE,IAAI,YAAY;IAChB,IAAI,CAAC,WAAW;QACf,MAAM,aAAa,IAAI,0IAAA,CAAA,aAAU;QACjC,YAAY,MAAM,WAAW,cAAc;IAC5C;IAEA,MAAM,gBAAgB,UAAU,SAAS;IACzC,MAAM,cAA0B,eAAe,SAAS,OAAO,SAAS,EAAE;IAE1E,MAAM,iBAAiB;QACtB,2BAA2B;QAC3B,QAAQ,GAAG,CAAC;IACb;IAEA,qBACC,8OAAC;QAAI,WAAW,GAAG,iJAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,EAAE,iJAAA,CAAA,UAAK,CAAC,gBAAgB,EAAE;;0BACxD,8OAAC;gBAAG,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;0BAAE;;;;;;0BAC5B,8OAAC;gBAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,SAAS;0BAC7B,YAAY,GAAG,CAAC,CAAC,qBACjB,8OAAC;wBAAkB,WAAW,iJAAA,CAAA,UAAK,CAAC,WAAW;kCAE9C,cAAA,8OAAC;4BAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,IAAI;;8CACzB,8OAAC;oCAAK,WAAW,iJAAA,CAAA,UAAK,CAAC,WAAW;8CAChC,KAAK,OAAO;;;;;;8CAEd,8OAAC;oCAAK,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;;wCAAE;wCACxB,KAAK,YAAY,EAAE;wCAAa;;;;;;;;;;;;;uBAP9B,KAAK,EAAE;;;;;;;;;;0BAanB,8OAAC;gBAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,QAAQ;0BAC7B,cAAA,8OAAC,+IAAA,CAAA,UAAc;oBAAC,YAAY;;;;;;;;;;;;;;;;;AAIhC;uCAEe", "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/main/ui/index.tsx"], "sourcesContent": ["import { CountryApi } from '@/src/entities/country/api'\nimport { FC } from 'react'\nimport HowItsWork from '../../howItWorks/ui'\nimport style from './index.module.css'\nimport PopularCountries from './popularCountries'\n\nconst MainContent: FC = async () => {\n    // Получаем данные на сервере для SSR\n    const countryApi = new CountryApi()\n    const countries = await countryApi.fetchCountries()\n\n    return (\n        <main className={style.main}>\n            <PopularCountries propsCountries={countries} />\n            <HowItsWork />\n        </main>\n    )\n}\n\nexport default MainContent\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AACA;;;;;;AAEA,MAAM,cAAkB;IACpB,qCAAqC;IACrC,MAAM,aAAa,IAAI,0IAAA,CAAA,aAAU;IACjC,MAAM,YAAY,MAAM,WAAW,cAAc;IAEjD,qBACI,8OAAC;QAAK,WAAW,iJAAA,CAAA,UAAK,CAAC,IAAI;;0BACvB,8OAAC,iJAAA,CAAA,UAAgB;gBAAC,gBAAgB;;;;;;0BAClC,8OAAC,4IAAA,CAAA,UAAU;;;;;;;;;;;AAGvB;uCAEe", "debugId": null}}, {"offset": {"line": 421, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/pages/home/<USER>/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"content\": \"index-module__vhjfEW__content\",\n  \"page\": \"index-module__vhjfEW__page\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 429, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/pages/home/<USER>/index.tsx"], "sourcesContent": ["import Header from '@/src/widgets/header/ui'\nimport Hero from '@/src/widgets/hero/ui'\nimport MainContent from '@/src/widgets/main/ui'\nimport { FC } from 'react'\nimport style from './index.module.css'\n\nconst HomePage: FC = () => {\n\treturn (\n\t\t<div className={style.page}>\n\t\t\t<Header />\n\t\t\t<div className={style.content}>\n\t\t\t\t<Hero />\n\t\t\t\t<MainContent />\n\t\t\t</div>\n\t\t</div>\n\t)\n}\n\nexport default HomePage\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,WAAe;IACpB,qBACC,8OAAC;QAAI,WAAW,+IAAA,CAAA,UAAK,CAAC,IAAI;;0BACzB,8OAAC,wIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAI,WAAW,+IAAA,CAAA,UAAK,CAAC,OAAO;;kCAC5B,8OAAC,sIAAA,CAAA,UAAI;;;;;kCACL,8OAAC,sIAAA,CAAA,UAAW;;;;;;;;;;;;;;;;;AAIhB;uCAEe", "debugId": null}}, {"offset": {"line": 482, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/app/page.tsx"], "sourcesContent": ["import HomePage from '@/src/pages/home/<USER>'\n\nexport default function Home() {\n\treturn <HomePage />\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACvB,qBAAO,8OAAC,oIAAA,CAAA,UAAQ;;;;;AACjB", "debugId": null}}]}