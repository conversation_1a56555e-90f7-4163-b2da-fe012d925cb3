{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/icons/search.tsx"], "sourcesContent": ["import Image from 'next/image'\nimport { FC } from 'react'\n\nconst SearchIcon: FC = () => {\n\treturn (\n\t\t<Image src={'/search-icon.svg'} alt='Search' width={20} height={20} />\n\t)\n}\n\nexport default SearchIcon\n"], "names": [], "mappings": ";;;;AAAA;;;AAGA,MAAM,aAAiB;IACtB,qBACC,8OAAC,6HAAA,CAAA,UAAK;QAAC,KAAK;QAAoB,KAAI;QAAS,OAAO;QAAI,QAAQ;;;;;;AAElE;uCAEe", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/shared/ui/input/index.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"input\": \"index-module__Bu1eGa__input\",\n  \"label\": \"index-module__Bu1eGa__label\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/input/index.tsx"], "sourcesContent": ["import { FC, HTMLInputTypeAttribute, ReactNode } from 'react'\nimport style from './index.module.css'\n\ninterface InputProps {\n\ttype?: HTMLInputTypeAttribute\n\tname?: string\n\tvalue?: string\n\tplaceholder?: string\n\tprefix?: ReactNode\n\tonChange?: (event: React.ChangeEvent<HTMLInputElement>) => void\n}\n\nconst Input: FC<InputProps> = ({\n\ttype,\n\tname,\n\tvalue,\n\tplaceholder,\n\tprefix,\n\tonChange\n}) => {\n\treturn (\n\t\t<label className={style.label}>\n\t\t\t{prefix}\n\t\t\t<input\n\t\t\t\tclassName={style.input}\n\t\t\t\ttype={type}\n\t\t\t\tname={name}\n\t\t\t\tvalue={value}\n\t\t\t\tplaceholder={placeholder}\n\t\t\t\tonChange={onChange}\n\t\t\t/>\n\t\t</label>\n\t)\n}\n\nexport default Input\n"], "names": [], "mappings": ";;;;AACA;;;AAWA,MAAM,QAAwB,CAAC,EAC9B,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,WAAW,EACX,MAAM,EACN,QAAQ,EACR;IACA,qBACC,8OAAC;QAAM,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;;YAC3B;0BACD,8OAAC;gBACA,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;gBACtB,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;;;;;;;;;;;;AAId;uCAEe", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/hero/ui/index.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"dropdown\": \"index-module__m10heG__dropdown\",\n  \"hero\": \"index-module__m10heG__hero\",\n  \"search\": \"index-module__m10heG__search\",\n  \"title\": \"index-module__m10heG__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/searchDropdown.tsx"], "sourcesContent": ["import { FC } from 'react';\nimport style from './index.module.css';\n\nconst SearchDropdown: FC = () => {\n    return (\n        <div className={style.dropdown}>\n\n        </div>\n    );\n}\n\nexport default SearchDropdown;"], "names": [], "mappings": ";;;;AACA;;;AAEA,MAAM,iBAAqB;IACvB,qBACI,8OAAC;QAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,QAAQ;;;;;;AAItC;uCAEe", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/search.tsx"], "sourcesContent": ["'use client'\n\nimport SearchIcon from \"@/src/shared/ui/icons/search\"\nimport Input from \"@/src/shared/ui/input\"\nimport style from './index.module.css'\nimport SearchDropdown from \"./searchDropdown\"\n\nconst HeroSearch = () => {\n    const { searchQuery, setSearchQuery } = useHeaderStore()\n\n    const handleSearch = (value: string) => {\n        setSearchQuery(value)\n        console.log(value)\n    }\n\n    return (\n        <div className={style.search}>\n            <Input\n                placeholder='Найти направление'\n                prefix={<SearchIcon />}\n                value={searchQuery}\n                onChange={(value) => handleSearch(value.target.value)}\n            />\n            <SearchDropdown />\n        </div>\n    )\n}\n\nexport default HeroSearch;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,MAAM,aAAa;IACf,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG;IAExC,MAAM,eAAe,CAAC;QAClB,eAAe;QACf,QAAQ,GAAG,CAAC;IAChB;IAEA,qBACI,8OAAC;QAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,MAAM;;0BACxB,8OAAC,sIAAA,CAAA,UAAK;gBACF,aAAY;gBACZ,sBAAQ,8OAAC,uIAAA,CAAA,UAAU;;;;;gBACnB,OAAO;gBACP,UAAU,CAAC,QAAU,aAAa,MAAM,MAAM,CAAC,KAAK;;;;;;0BAExD,8OAAC,+IAAA,CAAA,UAAc;;;;;;;;;;;AAG3B;uCAEe", "debugId": null}}]}