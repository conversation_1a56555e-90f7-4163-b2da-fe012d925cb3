{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/shared/ui/logo/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"logo\": \"index-module__ytjyqG__logo\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/logo/index.tsx"], "sourcesContent": ["import Image from 'next/image'\nimport { FC } from 'react'\nimport style from './index.module.css'\n\ninterface LogoProps {\n\twithType?: boolean\n}\n\nconst Logo: FC<LogoProps> = ({ withType = true }) => {\n\treturn (\n\t\t<div className={style.logo}>\n\t\t\t<Image\n\t\t\t\tsrc={'/yesim_circle.svg'}\n\t\t\t\talt='Logo'\n\t\t\t\twidth={30}\n\t\t\t\theight={30}\n\t\t\t/>\n\t\t\t{withType && (\n\t\t\t\t<Image\n\t\t\t\t\tsrc={'/yesim_type.svg'}\n\t\t\t\t\talt='Logo'\n\t\t\t\t\twidth={65}\n\t\t\t\t\theight={23}\n\t\t\t\t/>\n\t\t\t)}\n\t\t</div>\n\t)\n}\n\nexport default Logo\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAMA,MAAM,OAAsB,CAAC,EAAE,WAAW,IAAI,EAAE;IAC/C,qBACC,8OAAC;QAAI,WAAW,gJAAA,CAAA,UAAK,CAAC,IAAI;;0BACzB,8OAAC,6HAAA,CAAA,UAAK;gBACL,KAAK;gBACL,KAAI;gBACJ,OAAO;gBACP,QAAQ;;;;;;YAER,0BACA,8OAAC,6HAAA,CAAA,UAAK;gBACL,KAAK;gBACL,KAAI;gBACJ,OAAO;gBACP,QAAQ;;;;;;;;;;;;AAKb;uCAEe", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/header/ui/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"header\": \"index-module__6gvmdq__header\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/header/ui/index.tsx"], "sourcesContent": ["import Logo from '@/src/shared/ui/logo'\nimport { FC } from 'react'\nimport style from './index.module.css'\n\nconst Header: FC = () => {\n\treturn (\n\t\t<header className={style.header}>\n\t\t\t<Logo />\n\t\t</header>\n\t)\n}\n\nexport default Header\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,SAAa;IAClB,qBACC,8OAAC;QAAO,WAAW,mJAAA,CAAA,UAAK,CAAC,MAAM;kBAC9B,cAAA,8OAAC,qIAAA,CAAA,UAAI;;;;;;;;;;AAGR;uCAEe", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/hero/ui/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"dropdown\": \"index-module__m10heG__dropdown\",\n  \"hero\": \"index-module__m10heG__hero\",\n  \"search\": \"index-module__m10heG__search\",\n  \"title\": \"index-module__m10heG__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/icons/search.tsx"], "sourcesContent": ["import Image from 'next/image'\nimport { FC } from 'react'\n\nconst SearchIcon: FC = () => {\n\treturn (\n\t\t<Image src={'/search-icon.svg'} alt='Search' width={20} height={20} />\n\t)\n}\n\nexport default SearchIcon\n"], "names": [], "mappings": ";;;;AAAA;;;AAGA,MAAM,aAAiB;IACtB,qBACC,8OAAC,6HAAA,CAAA,UAAK;QAAC,KAAK;QAAoB,KAAI;QAAS,OAAO;QAAI,QAAQ;;;;;;AAElE;uCAEe", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/shared/ui/input/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"input\": \"index-module__Bu1eGa__input\",\n  \"label\": \"index-module__Bu1eGa__label\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/input/index.tsx"], "sourcesContent": ["import { FC, HTMLInputTypeAttribute, ReactNode } from 'react'\nimport style from './index.module.css'\n\ninterface InputProps {\n\ttype?: HTMLInputTypeAttribute\n\tname?: string\n\tvalue?: string\n\tplaceholder?: string\n\tprefix?: ReactNode\n\tonChange?: (event: React.ChangeEvent<HTMLInputElement>) => void\n}\n\nconst Input: FC<InputProps> = ({\n\ttype,\n\tname,\n\tvalue,\n\tplaceholder,\n\tprefix,\n\tonChange\n}) => {\n\treturn (\n\t\t<label className={style.label}>\n\t\t\t{prefix}\n\t\t\t<input\n\t\t\t\tclassName={style.input}\n\t\t\t\ttype={type}\n\t\t\t\tname={name}\n\t\t\t\tvalue={value}\n\t\t\t\tplaceholder={placeholder}\n\t\t\t\tonChange={onChange}\n\t\t\t/>\n\t\t</label>\n\t)\n}\n\nexport default Input\n"], "names": [], "mappings": ";;;;AACA;;;AAWA,MAAM,QAAwB,CAAC,EAC9B,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,WAAW,EACX,MAAM,EACN,QAAQ,EACR;IACA,qBACC,8OAAC;QAAM,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;;YAC3B;0BACD,8OAAC;gBACA,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;gBACtB,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;;;;;;;;;;;;AAId;uCAEe", "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/model/searchStore.ts"], "sourcesContent": ["\nimport { create } from 'zustand';\n\ninterface SearchStore {\n    searchQuery: string;\n    setSearchQuery: (value: string) => void;\n}\n\nexport const useSearchStore = create<SearchStore>((set) => ({\n    searchQuery: '',\n    setSearchQuery: (value) => set({ searchQuery: value }),\n}))\n"], "names": [], "mappings": ";;;AACA;;AAOO,MAAM,iBAAiB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAe,CAAC,MAAQ,CAAC;QACxD,aAAa;QACb,gBAAgB,CAAC,QAAU,IAAI;gBAAE,aAAa;YAAM;IACxD,CAAC", "debugId": null}}, {"offset": {"line": 194, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/searchDropdown.tsx"], "sourcesContent": ["import { FC } from 'react';\nimport style from './index.module.css';\n\ninterface SearchDropdownProps {\n    searchQuery: string;\n}\n\nconst SearchDropdown: FC<SearchDropdownProps> = ({\n    searchQuery,\n}) => {\n    return (\n        <div className={style.dropdown} data-is-visible={!!searchQuery.length}>\n\n        </div>\n    );\n}\n\nexport default SearchDropdown;"], "names": [], "mappings": ";;;;AACA;;;AAMA,MAAM,iBAA0C,CAAC,EAC7C,WAAW,EACd;IACG,qBACI,8OAAC;QAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,QAAQ;QAAE,mBAAiB,CAAC,CAAC,YAAY,MAAM;;;;;;AAI7E;uCAEe", "debugId": null}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/search.tsx"], "sourcesContent": ["// 'use client'\n\nimport SearchIcon from \"@/src/shared/ui/icons/search\"\nimport Input from \"@/src/shared/ui/input\"\nimport { useSearchStore } from \"../model/searchStore\"\nimport style from './index.module.css'\nimport SearchDropdown from \"./searchDropdown\"\n\nconst HeroSearch = () => {\n    const { searchQuery, setSearchQuery } = useSearchStore()\n\n    const handleSearch = (value: string) => {\n        setSearchQuery(value)\n        console.log(value)\n    }\n\n    return (\n        <div className={style.search}>\n            <Input\n                placeholder='Найти направление'\n                prefix={<SearchIcon />}\n                value={searchQuery}\n                onChange={(value) => handleSearch(value.target.value)}\n            />\n            <SearchDropdown searchQuery={searchQuery} />\n        </div>\n    )\n}\n\nexport default HeroSearch;\n"], "names": [], "mappings": "AAAA,eAAe;;;;;AAEf;AACA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,aAAa;IACf,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,iBAAc,AAAD;IAErD,MAAM,eAAe,CAAC;QAClB,eAAe;QACf,QAAQ,GAAG,CAAC;IAChB;IAEA,qBACI,8OAAC;QAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,MAAM;;0BACxB,8OAAC,sIAAA,CAAA,UAAK;gBACF,aAAY;gBACZ,sBAAQ,8OAAC,uIAAA,CAAA,UAAU;;;;;gBACnB,OAAO;gBACP,UAAU,CAAC,QAAU,aAAa,MAAM,MAAM,CAAC,KAAK;;;;;;0BAExD,8OAAC,+IAAA,CAAA,UAAc;gBAAC,aAAa;;;;;;;;;;;;AAGzC;uCAEe", "debugId": null}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/index.tsx"], "sourcesContent": ["import { FC } from 'react'\nimport style from './index.module.css'\nimport HeroSearch from './search'\n\nconst Hero: FC = () => {\n    return (\n        <div className={style.hero}>\n            <h1 className={style.title}>\n                eSIM карты с интернетом для путешествий и бизнеса\n            </h1>\n            <HeroSearch />\n        </div>\n    )\n}\n\nexport default Hero\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEA,MAAM,OAAW;IACb,qBACI,8OAAC;QAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,IAAI;;0BACtB,8OAAC;gBAAG,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;0BAAE;;;;;;0BAG5B,8OAAC,uIAAA,CAAA,UAAU;;;;;;;;;;;AAGvB;uCAEe", "debugId": null}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/howItWorks/ui/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"card\": \"index-module__aAZmyW__card\",\n  \"title\": \"index-module__aAZmyW__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/howItWorks/ui/index.tsx"], "sourcesContent": ["import { FC } from 'react'\nimport style from './index.module.css'\n\nconst HowItsWork: FC = () => {\n    return (\n        <div className={style.card}>\n            <h2 className={style.title}>Как это работает</h2>\n        </div>\n    )\n}\n\nexport default HowItsWork\n"], "names": [], "mappings": ";;;;AACA;;;AAEA,MAAM,aAAiB;IACnB,qBACI,8OAAC;QAAI,WAAW,uJAAA,CAAA,UAAK,CAAC,IAAI;kBACtB,cAAA,8OAAC;YAAG,WAAW,uJAAA,CAAA,UAAK,CAAC,KAAK;sBAAE;;;;;;;;;;;AAGxC;uCAEe", "debugId": null}}, {"offset": {"line": 347, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/main/ui/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"card\": \"index-module__aQ9zUq__card\",\n  \"loadMore\": \"index-module__aQ9zUq__loadMore\",\n  \"main\": \"index-module__aQ9zUq__main\",\n  \"title\": \"index-module__aQ9zUq__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 356, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/shared/ui/buttons/textButton/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"button\": \"index-module__mWR7qa__button\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 363, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/buttons/textButton/index.tsx"], "sourcesContent": ["import { FC } from 'react'\nimport style from './index.module.css'\n\ninterface TextButtonProps {\n\ttext: string\n\tonClick?: () => void\n}\n\nconst TextButton: FC<TextButtonProps> = ({ text, onClick }) => {\n\treturn (\n\t\t<button className={style.button} onClick={onClick}>\n\t\t\t{text}\n\t\t</button>\n\t)\n}\n\nexport default TextButton\n"], "names": [], "mappings": ";;;;AACA;;;AAOA,MAAM,aAAkC,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE;IACzD,qBACC,8OAAC;QAAO,WAAW,iKAAA,CAAA,UAAK,CAAC,MAAM;QAAE,SAAS;kBACxC;;;;;;AAGJ;uCAEe", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/main/ui/popularCountries.tsx"], "sourcesContent": ["import TextButton from '@/src/shared/ui/buttons/textButton'\nimport { FC } from 'react'\nimport style from './index.module.css'\n\nconst PopularCountries: FC = () => {\n\treturn (\n\t\t<div className={`${style.card} ${style.popularCountries}`}>\n\t\t\t<h2 className={style.title}>Популярные страны</h2>\n\t\t\t<div className={style.loadMore}>\n\t\t\t\t<TextButton text='Показать все страны' />\n\t\t\t</div>\n\t\t</div>\n\t)\n}\n\nexport default PopularCountries\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,mBAAuB;IAC5B,qBACC,8OAAC;QAAI,WAAW,GAAG,iJAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,EAAE,iJAAA,CAAA,UAAK,CAAC,gBAAgB,EAAE;;0BACxD,8OAAC;gBAAG,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;0BAAE;;;;;;0BAC5B,8OAAC;gBAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,QAAQ;0BAC7B,cAAA,8OAAC,sJAAA,CAAA,UAAU;oBAAC,MAAK;;;;;;;;;;;;;;;;;AAIrB;uCAEe", "debugId": null}}, {"offset": {"line": 433, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/main/ui/index.tsx"], "sourcesContent": ["import { FC } from 'react'\nimport HowItsWork from '../../howItWorks/ui'\nimport style from './index.module.css'\nimport PopularCountries from './popularCountries'\n\nconst MainContent: FC = () => {\n    return (\n        <main className={style.main}>\n            <PopularCountries />\n            <HowItsWork />\n        </main>\n    )\n}\n\nexport default MainContent\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAEA,MAAM,cAAkB;IACpB,qBACI,8OAAC;QAAK,WAAW,iJAAA,CAAA,UAAK,CAAC,IAAI;;0BACvB,8OAAC,iJAAA,CAAA,UAAgB;;;;;0BACjB,8OAAC,4IAAA,CAAA,UAAU;;;;;;;;;;;AAGvB;uCAEe", "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/pages/home/<USER>/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"content\": \"index-module__vhjfEW__content\",\n  \"page\": \"index-module__vhjfEW__page\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/pages/home/<USER>/index.tsx"], "sourcesContent": ["import Header from '@/src/widgets/header/ui'\nimport Hero from '@/src/widgets/hero/ui'\nimport MainContent from '@/src/widgets/main/ui'\nimport { FC } from 'react'\nimport style from './index.module.css'\n\nconst HomePage: FC = () => {\n\treturn (\n\t\t<div className={style.page}>\n\t\t\t<Header />\n\t\t\t<div className={style.content}>\n\t\t\t\t<Hero />\n\t\t\t\t<MainContent />\n\t\t\t</div>\n\t\t</div>\n\t)\n}\n\nexport default HomePage\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,WAAe;IACpB,qBACC,8OAAC;QAAI,WAAW,+IAAA,CAAA,UAAK,CAAC,IAAI;;0BACzB,8OAAC,wIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAI,WAAW,+IAAA,CAAA,UAAK,CAAC,OAAO;;kCAC5B,8OAAC,sIAAA,CAAA,UAAI;;;;;kCACL,8OAAC,sIAAA,CAAA,UAAW;;;;;;;;;;;;;;;;;AAIhB;uCAEe", "debugId": null}}, {"offset": {"line": 530, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/app/page.tsx"], "sourcesContent": ["import HomePage from '@/src/pages/home/<USER>'\n\nexport default function Home() {\n\treturn <HomePage />\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACvB,qBAAO,8OAAC,oIAAA,CAAA,UAAQ;;;;;AACjB", "debugId": null}}]}