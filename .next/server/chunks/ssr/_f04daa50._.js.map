{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from 'next'\nimport './fonts.css'\nimport './globals.css'\n\nexport const metadata: Metadata = {\n\ttitle: 'Yesim',\n\tdescription: 'eSIM карты с интернетом для путешествий и бизнеса'\n}\n\nexport default function RootLayout({\n\tchildren\n}: Readonly<{\n\tchildren: React.ReactNode\n}>) {\n\treturn (\n\t\t<html lang='ru'>\n\t\t\t<body>{children}</body>\n\t\t</html>\n\t)\n}\n"], "names": [], "mappings": ";;;;;;;;AAIO,MAAM,WAAqB;IACjC,OAAO;IACP,aAAa;AACd;AAEe,SAAS,WAAW,EAClC,QAAQ,EAGP;IACD,qBACC,8OAAC;QAAK,MAAK;kBACV,cAAA,8OAAC;sBAAM;;;;;;;;;;;AAGV", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}