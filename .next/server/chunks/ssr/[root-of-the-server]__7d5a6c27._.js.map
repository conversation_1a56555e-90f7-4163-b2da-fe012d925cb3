{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/shared/ui/logo/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"logo\": \"index-module__ytjyqG__logo\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/logo/index.tsx"], "sourcesContent": ["import Image from 'next/image'\nimport { FC } from 'react'\nimport style from './index.module.css'\n\ninterface LogoProps {\n\twithType?: boolean\n}\n\nconst Logo: FC<LogoProps> = ({ withType = true }) => {\n\treturn (\n\t\t<div className={style.logo}>\n\t\t\t<Image\n\t\t\t\tsrc={'/yesim_circle.svg'}\n\t\t\t\talt='Logo'\n\t\t\t\twidth={30}\n\t\t\t\theight={30}\n\t\t\t/>\n\t\t\t{withType && (\n\t\t\t\t<Image\n\t\t\t\t\tsrc={'/yesim_type.svg'}\n\t\t\t\t\talt='Logo'\n\t\t\t\t\twidth={65}\n\t\t\t\t\theight={23}\n\t\t\t\t/>\n\t\t\t)}\n\t\t</div>\n\t)\n}\n\nexport default Logo\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAMA,MAAM,OAAsB,CAAC,EAAE,WAAW,IAAI,EAAE;IAC/C,qBACC,8OAAC;QAAI,WAAW,gJAAA,CAAA,UAAK,CAAC,IAAI;;0BACzB,8OAAC,6HAAA,CAAA,UAAK;gBACL,KAAK;gBACL,KAAI;gBACJ,OAAO;gBACP,QAAQ;;;;;;YAER,0BACA,8OAAC,6HAAA,CAAA,UAAK;gBACL,KAAK;gBACL,KAAI;gBACJ,OAAO;gBACP,QAAQ;;;;;;;;;;;;AAKb;uCAEe", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/header/ui/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"header\": \"index-module__6gvmdq__header\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/header/ui/index.tsx"], "sourcesContent": ["import Logo from '@/src/shared/ui/logo'\nimport { FC } from 'react'\nimport style from './index.module.css'\n\nconst Header: FC = () => {\n\treturn (\n\t\t<header className={style.header}>\n\t\t\t<Logo />\n\t\t</header>\n\t)\n}\n\nexport default Header\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,SAAa;IAClB,qBACC,8OAAC;QAAO,WAAW,mJAAA,CAAA,UAAK,CAAC,MAAM;kBAC9B,cAAA,8OAAC,qIAAA,CAAA,UAAI;;;;;;;;;;AAGR;uCAEe", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/hero/ui/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"dropdown\": \"index-module__m10heG__dropdown\",\n  \"hero\": \"index-module__m10heG__hero\",\n  \"notFound\": \"index-module__m10heG__notFound\",\n  \"search\": \"index-module__m10heG__search\",\n  \"title\": \"index-module__m10heG__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/search.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/widgets/hero/ui/search.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/widgets/hero/ui/search.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/search.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/widgets/hero/ui/search.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/widgets/hero/ui/search.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/index.tsx"], "sourcesContent": ["import { FC } from 'react'\nimport style from './index.module.css'\nimport HeroSearch from './search'\n\nconst Hero: FC = () => {\n\treturn (\n\t\t<div className={style.hero}>\n\t\t\t<h1 className={style.title}>\n\t\t\t\teSIM карты с интернетом для путешествий и бизнеса\n\t\t\t</h1>\n\t\t\t<HeroSearch />\n\t\t</div>\n\t)\n}\n\nexport default Hero\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEA,MAAM,OAAW;IAChB,qBACC,8OAAC;QAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,IAAI;;0BACzB,8OAAC;gBAAG,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;0BAAE;;;;;;0BAG5B,8OAAC,uIAAA,CAAA,UAAU;;;;;;;;;;;AAGd;uCAEe", "debugId": null}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/howItWorks/ui/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"card\": \"index-module__aAZmyW__card\",\n  \"title\": \"index-module__aAZmyW__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/howItWorks/ui/index.tsx"], "sourcesContent": ["import { FC } from 'react'\nimport style from './index.module.css'\n\nconst HowItsWork: FC = () => {\n\treturn (\n\t\t<div className={style.card}>\n\t\t\t<h2 className={style.title}>Как это работает</h2>\n\t\t</div>\n\t)\n}\n\nexport default HowItsWork\n"], "names": [], "mappings": ";;;;AACA;;;AAEA,MAAM,aAAiB;IACtB,qBACC,8OAAC;QAAI,WAAW,uJAAA,CAAA,UAAK,CAAC,IAAI;kBACzB,cAAA,8OAAC;YAAG,WAAW,uJAAA,CAAA,UAAK,CAAC,KAAK;sBAAE;;;;;;;;;;;AAG/B;uCAEe", "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/main/ui/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"card\": \"index-module__aQ9zUq__card\",\n  \"loadMore\": \"index-module__aQ9zUq__loadMore\",\n  \"main\": \"index-module__aQ9zUq__main\",\n  \"title\": \"index-module__aQ9zUq__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/entities/country/api/index.ts"], "sourcesContent": ["import { CountriesI } from '../model/country'\n\nexport class CountryApi {\n\tasync fetchCountries(): Promise<CountriesI> {\n\t\tconst res = await fetch(\n\t\t\t'https://api3.yesim.cc/sale_list?force_type=countries&lang=ru'\n\t\t)\n\t\tconst data: CountriesI = await res.json()\n\t\treturn data\n\t}\n}\n"], "names": [], "mappings": ";;;AAEO,MAAM;IACZ,MAAM,iBAAsC;QAC3C,MAAM,MAAM,MAAM,MACjB;QAED,MAAM,OAAmB,MAAM,IAAI,IAAI;QACvC,OAAO;IACR;AACD", "debugId": null}}, {"offset": {"line": 241, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/entities/country/ui/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n});\n"], "names": [], "mappings": "AAAA;AACA"}}, {"offset": {"line": 247, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/entities/country/ui/card.tsx"], "sourcesContent": ["// 'use client'\n\nimport { FC } from 'react'\nimport { CountryI } from '../model/country'\nimport style from './index.module.css'\n\ninterface CountryCardProps {\n    country: CountryI\n    onClick: () => void\n}\n\nconst CountryCard: FC<CountryCardProps> = ({ country, onClick }) => {\n    return (\n        <button onClick={onClick} className={style.card}>\n            <span className={style.title}>{country.country}</span>\n        </button>\n    )\n}\n\nexport default CountryCard\n"], "names": [], "mappings": "AAAA,eAAe;;;;;AAIf;;;AAOA,MAAM,cAAoC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE;IAC3D,qBACI,8OAAC;QAAO,SAAS;QAAS,WAAW,qJAAA,CAAA,UAAK,CAAC,IAAI;kBAC3C,cAAA,8OAAC;YAAK,WAAW,qJAAA,CAAA,UAAK,CAAC,KAAK;sBAAG,QAAQ,OAAO;;;;;;;;;;;AAG1D;uCAEe", "debugId": null}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/shared/ui/buttons/textButton/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"button\": \"index-module__mWR7qa__button\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/buttons/textButton/index.tsx"], "sourcesContent": ["import { FC } from 'react'\nimport style from './index.module.css'\n\ninterface TextButtonProps {\n\ttext: string\n\tonClick?: () => void\n}\n\nconst TextButton: FC<TextButtonProps> = ({ text, onClick }) => {\n\treturn (\n\t\t<button className={style.button} onClick={onClick}>\n\t\t\t{text}\n\t\t</button>\n\t)\n}\n\nexport default TextButton\n"], "names": [], "mappings": ";;;;AACA;;;AAOA,MAAM,aAAkC,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE;IACzD,qBACC,8OAAC;QAAO,WAAW,iKAAA,CAAA,UAAK,CAAC,MAAM;QAAE,SAAS;kBACxC;;;;;;AAGJ;uCAEe", "debugId": null}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/main/ui/popularCountries.tsx"], "sourcesContent": ["import { CountryApi } from '@/src/entities/country/api'\nimport { CountryI } from '@/src/entities/country/model/country'\nimport CountryCard from '@/src/entities/country/ui/card'\nimport TextButton from '@/src/shared/ui/buttons/textButton'\nimport { FC } from 'react'\nimport style from './index.module.css'\n\nconst PopularCountries: FC = async () => {\n\t// for seo\n\tconst countryApi = new CountryApi()\n\tconst countries = await countryApi.fetchCountries()\n\n\tconst countriesData = countries.countries\n\tconst ruCountries: CountryI[] = countriesData?.['ru'] || []\n\n\treturn (\n\t\t<div className={`${style.card} ${style.popularCountries}`}>\n\t\t\t<h2 className={style.title}>Популярные страны</h2>\n\t\t\t<div className={style.countries}>\n\t\t\t\t{ruCountries.map(data => (\n\t\t\t\t\t<CountryCard\n\t\t\t\t\t\tkey={data.id}\n\t\t\t\t\t\tcountry={data}\n\t\t\t\t\t/>\n\t\t\t\t))}\n\t\t\t</div>\n\t\t\t<div className={style.loadMore}>\n\t\t\t\t<TextButton text='Показать все страны' />\n\t\t\t</div>\n\t\t</div>\n\t)\n}\n\nexport default PopularCountries\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AAEA;;;;;;AAEA,MAAM,mBAAuB;IAC5B,UAAU;IACV,MAAM,aAAa,IAAI,0IAAA,CAAA,aAAU;IACjC,MAAM,YAAY,MAAM,WAAW,cAAc;IAEjD,MAAM,gBAAgB,UAAU,SAAS;IACzC,MAAM,cAA0B,eAAe,CAAC,KAAK,IAAI,EAAE;IAE3D,qBACC,8OAAC;QAAI,WAAW,GAAG,iJAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,EAAE,iJAAA,CAAA,UAAK,CAAC,gBAAgB,EAAE;;0BACxD,8OAAC;gBAAG,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;0BAAE;;;;;;0BAC5B,8OAAC;gBAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,SAAS;0BAC7B,YAAY,GAAG,CAAC,CAAA,qBAChB,8OAAC,yIAAA,CAAA,UAAW;wBAEX,SAAS;uBADJ,KAAK,EAAE;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,QAAQ;0BAC7B,cAAA,8OAAC,sJAAA,CAAA,UAAU;oBAAC,MAAK;;;;;;;;;;;;;;;;;AAIrB;uCAEe", "debugId": null}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/main/ui/index.tsx"], "sourcesContent": ["import { FC } from 'react'\nimport HowItsWork from '../../howItWorks/ui'\nimport style from './index.module.css'\nimport PopularCountries from './popularCountries'\n\nconst MainContent: FC = () => {\n\t// const { countries, setCountries } = useCountriesStore()\n\n\t// const fetchCountries = async () => {\n\t//     const countryApi = new CountryApi()\n\t//     const countries = await countryApi.fetchCountries()\n\t//     setCountries(countries)\n\t// }\n\n\t// useEffect(() => {\n\t//     fetchCountries()\n\t// }, [])\n\n\t// const showMore = () => {\n\t//     console.log('show more')\n\t// }\n\n\treturn (\n\t\t<main className={style.main}>\n\t\t\t<PopularCountries />\n\t\t\t<HowItsWork />\n\t\t</main>\n\t)\n}\n\nexport default MainContent\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAEA,MAAM,cAAkB;IACvB,0DAA0D;IAE1D,uCAAuC;IACvC,0CAA0C;IAC1C,0DAA0D;IAC1D,8BAA8B;IAC9B,IAAI;IAEJ,oBAAoB;IACpB,uBAAuB;IACvB,SAAS;IAET,2BAA2B;IAC3B,+BAA+B;IAC/B,IAAI;IAEJ,qBACC,8OAAC;QAAK,WAAW,iJAAA,CAAA,UAAK,CAAC,IAAI;;0BAC1B,8OAAC,iJAAA,CAAA,UAAgB;;;;;0BACjB,8OAAC,4IAAA,CAAA,UAAU;;;;;;;;;;;AAGd;uCAEe", "debugId": null}}, {"offset": {"line": 425, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/pages/home/<USER>/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"content\": \"index-module__vhjfEW__content\",\n  \"page\": \"index-module__vhjfEW__page\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 433, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/pages/home/<USER>/index.tsx"], "sourcesContent": ["import Header from '@/src/widgets/header/ui'\nimport Hero from '@/src/widgets/hero/ui'\nimport MainContent from '@/src/widgets/main/ui'\nimport { FC } from 'react'\nimport style from './index.module.css'\n\nconst HomePage: FC = () => {\n\treturn (\n\t\t<div className={style.page}>\n\t\t\t<Header />\n\t\t\t<div className={style.content}>\n\t\t\t\t<Hero />\n\t\t\t\t<MainContent />\n\t\t\t</div>\n\t\t</div>\n\t)\n}\n\nexport default HomePage\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,WAAe;IACpB,qBACC,8OAAC;QAAI,WAAW,+IAAA,CAAA,UAAK,CAAC,IAAI;;0BACzB,8OAAC,wIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAI,WAAW,+IAAA,CAAA,UAAK,CAAC,OAAO;;kCAC5B,8OAAC,sIAAA,CAAA,UAAI;;;;;kCACL,8OAAC,sIAAA,CAAA,UAAW;;;;;;;;;;;;;;;;;AAIhB;uCAEe", "debugId": null}}, {"offset": {"line": 486, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/app/page.tsx"], "sourcesContent": ["import HomePage from '@/src/pages/home/<USER>'\n\nexport default function Home() {\n\treturn <HomePage />\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACvB,qBAAO,8OAAC,oIAAA,CAAA,UAAQ;;;;;AACjB", "debugId": null}}]}