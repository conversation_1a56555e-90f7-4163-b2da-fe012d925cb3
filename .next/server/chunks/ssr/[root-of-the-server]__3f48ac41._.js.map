{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/shared/ui/logo/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"logo\": \"index-module__ytjyqG__logo\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/logo/index.tsx"], "sourcesContent": ["import Image from 'next/image'\nimport { FC } from 'react'\nimport style from './index.module.css'\n\ninterface LogoProps {\n\twithType?: boolean\n}\n\nconst Logo: FC<LogoProps> = ({ withType = true }) => {\n\treturn (\n\t\t<div className={style.logo}>\n\t\t\t<Image\n\t\t\t\tsrc={'/yesim_circle.svg'}\n\t\t\t\talt='Logo'\n\t\t\t\twidth={30}\n\t\t\t\theight={30}\n\t\t\t/>\n\t\t\t{withType && (\n\t\t\t\t<Image\n\t\t\t\t\tsrc={'/yesim_type.svg'}\n\t\t\t\t\talt='Logo'\n\t\t\t\t\twidth={65}\n\t\t\t\t\theight={23}\n\t\t\t\t/>\n\t\t\t)}\n\t\t</div>\n\t)\n}\n\nexport default Logo\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAMA,MAAM,OAAsB,CAAC,EAAE,WAAW,IAAI,EAAE;IAC/C,qBACC,8OAAC;QAAI,WAAW,gJAAA,CAAA,UAAK,CAAC,IAAI;;0BACzB,8OAAC,6HAAA,CAAA,UAAK;gBACL,KAAK;gBACL,KAAI;gBACJ,OAAO;gBACP,QAAQ;;;;;;YAER,0BACA,8OAAC,6HAAA,CAAA,UAAK;gBACL,KAAK;gBACL,KAAI;gBACJ,OAAO;gBACP,QAAQ;;;;;;;;;;;;AAKb;uCAEe", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/header/ui/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"header\": \"index-module__6gvmdq__header\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/header/ui/index.tsx"], "sourcesContent": ["import Logo from '@/src/shared/ui/logo'\nimport { FC } from 'react'\nimport style from './index.module.css'\n\nconst Header: FC = () => {\n\treturn (\n\t\t<header className={style.header}>\n\t\t\t<Logo />\n\t\t</header>\n\t)\n}\n\nexport default Header\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,SAAa;IAClB,qBACC,8OAAC;QAAO,WAAW,mJAAA,CAAA,UAAK,CAAC,MAAM;kBAC9B,cAAA,8OAAC,qIAAA,CAAA,UAAI;;;;;;;;;;AAGR;uCAEe", "debugId": null}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/icons/search.tsx"], "sourcesContent": ["import Image from 'next/image'\nimport { FC } from 'react'\n\nconst SearchIcon: FC = () => {\n    return (\n        <Image\n            src={'/search-icon.svg'}\n            alt='Search'\n            width={20}\n            height={20}\n        />\n    )\n}\n\nexport default SearchIcon\n"], "names": [], "mappings": ";;;;AAAA;;;AAGA,MAAM,aAAiB;IACnB,qBACI,8OAAC,6HAAA,CAAA,UAAK;QACF,KAAK;QACL,KAAI;QACJ,OAAO;QACP,QAAQ;;;;;;AAGpB;uCAEe", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/shared/ui/input/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"input\": \"index-module__Bu1eGa__input\",\n  \"label\": \"index-module__Bu1eGa__label\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/input/index.tsx"], "sourcesContent": ["import { FC, HTMLInputTypeAttribute, ReactNode } from \"react\";\nimport style from './index.module.css';\n\n\ninterface InputProps {\n    type?: HTMLInputTypeAttribute;\n    name?: string;\n    value?: string;\n    placeholder?: string;\n    prefix?: ReactNode;\n    onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;\n}\n\nconst Input: FC<InputProps> = ({\n    type,\n    name,\n    value,\n    placeholder,\n    prefix,\n    onChange\n}) => {\n    return (\n        <label className={style.label}>\n            {prefix}\n            <input\n                className={style.input}\n                type={type}\n                name={name}\n                value={value}\n                placeholder={placeholder}\n                onChange={onChange}\n            />\n        </label>\n    );\n}\n\nexport default Input;"], "names": [], "mappings": ";;;;AACA;;;AAYA,MAAM,QAAwB,CAAC,EAC3B,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,WAAW,EACX,MAAM,EACN,QAAQ,EACX;IACG,qBACI,8OAAC;QAAM,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;;YACxB;0BACD,8OAAC;gBACG,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;gBACtB,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;;;;;;;;;;;;AAI1B;uCAEe", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/hero/ui/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"hero\": \"index-module__m10heG__hero\",\n  \"title\": \"index-module__m10heG__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/index.tsx"], "sourcesContent": ["import SearchIcon from '@/src/shared/ui/icons/search'\nimport Input from '@/src/shared/ui/input'\nimport { FC } from 'react'\nimport style from './index.module.css'\n\nconst Hero: FC = () => {\n    return (\n        <div className={style.hero}>\n            <h1 className={style.title}>\n                eSIM карты с интернетом для путешествий и бизнеса\n            </h1>\n            <Input\n                placeholder='Найти направление'\n                prefix={<SearchIcon />}\n            />\n        </div>\n    )\n}\n\nexport default Hero\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,OAAW;IACb,qBACI,8OAAC;QAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,IAAI;;0BACtB,8OAAC;gBAAG,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;0BAAE;;;;;;0BAG5B,8OAAC,sIAAA,CAAA,UAAK;gBACF,aAAY;gBACZ,sBAAQ,8OAAC,uIAAA,CAAA,UAAU;;;;;;;;;;;;;;;;AAInC;uCAEe", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/main/ui/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"card\": \"index-module__aQ9zUq__card\",\n  \"main\": \"index-module__aQ9zUq__main\",\n  \"title\": \"index-module__aQ9zUq__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 232, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/main/ui/popularCountries.tsx"], "sourcesContent": ["import { FC } from 'react';\nimport style from './index.module.css';\n\nconst PopularCountries: FC = () => {\n    return (<div className={`${style.card} ${style.popularCountries}`}>\n        <h2 className={style.title}>\n            Популярные страны\n        </h2>\n    </div>);\n}\n\nexport default PopularCountries;"], "names": [], "mappings": ";;;;AACA;;;AAEA,MAAM,mBAAuB;IACzB,qBAAQ,8OAAC;QAAI,WAAW,GAAG,iJAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,EAAE,iJAAA,CAAA,UAAK,CAAC,gBAAgB,EAAE;kBAC7D,cAAA,8OAAC;YAAG,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;sBAAE;;;;;;;;;;;AAIpC;uCAEe", "debugId": null}}, {"offset": {"line": 261, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/main/ui/index.tsx"], "sourcesContent": ["import { FC } from 'react';\nimport style from './index.module.css';\nimport PopularCountries from './popularCountries';\n\nconst MainContent: FC = () => {\n    return (<main className={style.main}>\n        <PopularCountries />\n    </main>);\n}\n\nexport default MainContent;"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEA,MAAM,cAAkB;IACpB,qBAAQ,8OAAC;QAAK,WAAW,iJAAA,CAAA,UAAK,CAAC,IAAI;kBAC/B,cAAA,8OAAC,iJAAA,CAAA,UAAgB;;;;;;;;;;AAEzB;uCAEe", "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/pages/home/<USER>/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"content\": \"index-module__vhjfEW__content\",\n  \"page\": \"index-module__vhjfEW__page\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 296, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/pages/home/<USER>/index.tsx"], "sourcesContent": ["import Header from '@/src/widgets/header/ui'\nimport Hero from '@/src/widgets/hero/ui'\nimport MainContent from '@/src/widgets/main/ui'\nimport { FC } from 'react'\nimport style from './index.module.css'\n\nconst HomePage: FC = () => {\n    return (\n        <div className={style.page}>\n            <Header />\n            <div className={style.content}>\n                <Hero />\n                <MainContent />\n            </div>\n        </div>\n    )\n}\n\nexport default HomePage\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,WAAe;IACjB,qBACI,8OAAC;QAAI,WAAW,+IAAA,CAAA,UAAK,CAAC,IAAI;;0BACtB,8OAAC,wIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAI,WAAW,+IAAA,CAAA,UAAK,CAAC,OAAO;;kCACzB,8OAAC,sIAAA,CAAA,UAAI;;;;;kCACL,8OAAC,sIAAA,CAAA,UAAW;;;;;;;;;;;;;;;;;AAI5B;uCAEe", "debugId": null}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/app/page.tsx"], "sourcesContent": ["import HomePage from '@/src/pages/home/<USER>'\n\nexport default function Home() {\n\treturn <HomePage />\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACvB,qBAAO,8OAAC,oIAAA,CAAA,UAAQ;;;;;AACjB", "debugId": null}}]}