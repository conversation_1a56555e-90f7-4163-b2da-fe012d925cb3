{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/header/ui/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"header\": \"index-module__6gvmdq__header\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/header/ui/index.tsx"], "sourcesContent": ["import { FC } from 'react'\nimport style from './index.module.css'\n\nconst Header: FC = () => {\n    return <header className={style.header}>\n    </header>\n}\n\nexport default Header\n"], "names": [], "mappings": ";;;;AACA;;;AAEA,MAAM,SAAa;IACf,qBAAO,8OAAC;QAAO,WAAW,mJAAA,CAAA,UAAK,CAAC,MAAM;;;;;;AAE1C;uCAEe", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/page.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"page\": \"page-module__E0kJGG__page\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/app/page.tsx"], "sourcesContent": ["import Header from '@/src/widgets/header/ui'\nimport styles from './page.module.css'\n\nexport default function Home() {\n\treturn (\n\t\t<div className={styles.page}>\n\t\t\t<Header />\n\t\t</div>\n\t)\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACvB,qBACC,8OAAC;QAAI,WAAW,uHAAA,CAAA,UAAM,CAAC,IAAI;kBAC1B,cAAA,8OAAC,wIAAA,CAAA,UAAM;;;;;;;;;;AAGV", "debugId": null}}]}