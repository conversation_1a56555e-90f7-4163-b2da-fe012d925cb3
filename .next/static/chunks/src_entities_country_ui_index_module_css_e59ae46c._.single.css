/* [project]/src/entities/country/ui/index.module.css [app-client] (css) */
.index-module__M6SB_q__card {
  background: var(--surface);
  border-radius: 1.25rem;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  width: 100%;
  height: 3.625rem;
  padding: 0 1rem;
  display: flex;
}

.index-module__M6SB_q__content {
  align-items: center;
  gap: 1rem;
  display: flex;
}

.index-module__M6SB_q__flagWrapper {
  background: var(--background);
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  overflow: hidden;
}

.index-module__M6SB_q__info {
  flex-direction: column;
  align-items: flex-start;
  gap: .125rem;
  display: flex;
}

.index-module__M6SB_q__title {
  font-size: .875rem;
  font-weight: 500;
  line-height: 1.125rem;
}

.index-module__M6SB_q__price {
  font-size: var(--font-caption-size);
  font-weight: var(--font-caption-weight);
  line-height: var(--font-caption-line-height);
  letter-spacing: var(--font-caption-letter-spacing);
}

/*# sourceMappingURL=src_entities_country_ui_index_module_css_e59ae46c._.single.css.map*/