/* [project]/src/widgets/main/ui/index.module.css [app-client] (css) */
.index-module__aQ9zUq__main {
  flex-direction: column;
  gap: .5rem;
  padding: .5rem 0 1.5rem;
  display: flex;
}

.index-module__aQ9zUq__card {
  background: var(--surface);
  border-radius: 1.25rem;
  flex-direction: column;
  display: flex;
}

.index-module__aQ9zUq__title {
  font-size: var(--font-subtitle-size);
  font-weight: var(--font-subtitle-weight);
  line-height: var(--font-subtitle-line-height);
  letter-spacing: var(--font-subtitle-letter-spacing);
  padding: 1rem 1rem .75rem;
}

.index-module__aQ9zUq__loadMore {
  padding: .75rem 1rem 1rem;
}

.index-module__aQ9zUq__popularCountries, .index-module__aQ9zUq__countries {
  flex-direction: column;
  display: flex;
}

/*# sourceMappingURL=src_widgets_main_ui_index_module_css_e59ae46c._.single.css.map*/