{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/hero/ui/index.module.css"], "sourcesContent": [".hero {\n\tpadding: 1.25rem 0 1rem 0;\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 2rem;\n}\n\n.title {\n\tfont-size: var(--font-title-size);\n\tfont-weight: var(--font-title-weight);\n\tline-height: var(--font-title-line-height);\n\tletter-spacing: var(--font-title-letter-spacing);\n\ttext-align: center;\n\ttext-wrap: balance;\n}\n\n.search {\n\tposition: relative;\n}\n\n.dropdown {\n\t&[data-is-visible='false'] {\n\t\topacity: 0;\n\t\tpointer-events: none;\n\t}\n\n\ttransition: opacity 0.2s ease;\n\topacity: 1;\n\tposition: absolute;\n\ttop: calc(100% + 0.375rem);\n\tleft: 0;\n\twidth: 100%;\n\tbackground: var(--surface);\n\tmin-height: 8.125rem;\n\tmax-height: 18.75rem;\n\n\tbox-shadow: 0 0.5rem 0.625rem 0.25rem rgba(0, 0, 0, 0.1);\n\n\tdisplay: flex;\n\tflex-direction: column;\n\tborder-radius: 1.25rem;\n}\n\n.notFound {\n\tmargin: auto;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;AAOA;;;;;;;;;AASA;;;;AAKC;;;;;AAKW;;;;;;;;;;;;;;;;AAiBZ"}}]}