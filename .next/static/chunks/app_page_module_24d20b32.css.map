{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/app/page.module.css"], "sourcesContent": [".page {\n\t--gray-rgb: 0, 0, 0;\n\t--gray-alpha-200: rgba(var(--gray-rgb), 0.08);\n\t--gray-alpha-100: rgba(var(--gray-rgb), 0.05);\n\n\t--button-primary-hover: #383838;\n\t--button-secondary-hover: #f2f2f2;\n\n\tdisplay: grid;\n\tgrid-template-rows: 20px 1fr 20px;\n\talign-items: center;\n\tjustify-items: center;\n\tmin-height: 100svh;\n\tpadding: 80px;\n\tgap: 64px;\n\tfont-family: var(--font-geist-sans);\n}\n\n@media (prefers-color-scheme: dark) {\n\t.page {\n\t\t--gray-rgb: 255, 255, 255;\n\t\t--gray-alpha-200: rgba(var(--gray-rgb), 0.145);\n\t\t--gray-alpha-100: rgba(var(--gray-rgb), 0.06);\n\n\t\t--button-primary-hover: #ccc;\n\t\t--button-secondary-hover: #1a1a1a;\n\t}\n}\n\n.main {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 32px;\n\tgrid-row-start: 2;\n}\n\n.main ol {\n\tfont-family: var(--font-geist-mono);\n\tpadding-left: 0;\n\tmargin: 0;\n\tfont-size: 14px;\n\tline-height: 24px;\n\tletter-spacing: -0.01em;\n\tlist-style-position: inside;\n}\n\n.main li:not(:last-of-type) {\n\tmargin-bottom: 8px;\n}\n\n.main code {\n\tfont-family: inherit;\n\tbackground: var(--gray-alpha-100);\n\tpadding: 2px 4px;\n\tborder-radius: 4px;\n\tfont-weight: 600;\n}\n\n.ctas {\n\tdisplay: flex;\n\tgap: 16px;\n}\n\n.ctas a {\n\tappearance: none;\n\tborder-radius: 128px;\n\theight: 48px;\n\tpadding: 0 20px;\n\tborder: 1px solid transparent;\n\ttransition:\n\t\tbackground 0.2s,\n\t\tcolor 0.2s,\n\t\tborder-color 0.2s;\n\tcursor: pointer;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 16px;\n\tline-height: 20px;\n\tfont-weight: 500;\n}\n\na.primary {\n\tbackground: var(--foreground);\n\tcolor: var(--background);\n\tgap: 8px;\n}\n\na.secondary {\n\tborder-color: var(--gray-alpha-200);\n\tmin-width: 158px;\n}\n\n.footer {\n\tgrid-row-start: 3;\n\tdisplay: flex;\n\tgap: 24px;\n}\n\n.footer a {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8px;\n}\n\n.footer img {\n\tflex-shrink: 0;\n}\n\n/* Enable hover only on non-touch devices */\n@media (hover: hover) and (pointer: fine) {\n\ta.primary:hover {\n\t\tbackground: var(--button-primary-hover);\n\t\tborder-color: transparent;\n\t}\n\n\ta.secondary:hover {\n\t\tbackground: var(--button-secondary-hover);\n\t\tborder-color: transparent;\n\t}\n\n\t.footer a:hover {\n\t\ttext-decoration: underline;\n\t\ttext-underline-offset: 4px;\n\t}\n}\n\n@media (max-width: 600px) {\n\t.page {\n\t\tpadding: 32px;\n\t\tpadding-bottom: 80px;\n\t}\n\n\t.main {\n\t\talign-items: center;\n\t}\n\n\t.main ol {\n\t\ttext-align: center;\n\t}\n\n\t.ctas {\n\t\tflex-direction: column;\n\t}\n\n\t.ctas a {\n\t\tfont-size: 14px;\n\t\theight: 40px;\n\t\tpadding: 0 16px;\n\t}\n\n\ta.secondary {\n\t\tmin-width: auto;\n\t}\n\n\t.footer {\n\t\tflex-wrap: wrap;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n}\n\n@media (prefers-color-scheme: dark) {\n\t.logo {\n\t\tfilter: invert();\n\t}\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;AAkBA;EACC;;;;;;;;;AAUD;;;;;;;AAOA;;;;;;;;;;AAUA;;;;AAIA;;;;;;;;AAQA;;;;;AAKA;;;;;;;;;;;;;;;;;;AAmBA;;;;;;AAMA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;AAKA;EACC;;;;;EAKA;;;;;EAKA;;;;;;AAMD;EACC;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;;;;AAOD;EACC", "debugId": null}}]}