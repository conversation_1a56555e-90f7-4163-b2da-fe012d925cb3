{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/main/ui/index.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"card\": \"index-module__aQ9zUq__card\",\n  \"loadMore\": \"index-module__aQ9zUq__loadMore\",\n  \"main\": \"index-module__aQ9zUq__main\",\n  \"title\": \"index-module__aQ9zUq__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/shared/ui/buttons/textButton/index.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"button\": \"index-module__mWR7qa__button\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/buttons/textButton/index.tsx"], "sourcesContent": ["import { FC } from \"react\";\nimport style from './index.module.css';\n\ninterface TextButtonProps {\n    text: string;\n    onClick?: () => void;\n}\n\nconst TextButton: FC<TextButtonProps> = ({\n    text,\n    onClick,\n}) => {\n    return (\n        <button className={style.button} onClick={onClick}>\n            {text}\n        </button>\n    );\n}\n\nexport default TextButton;"], "names": [], "mappings": ";;;;AACA;;;AAOA,MAAM,aAAkC;QAAC,EACrC,IAAI,EACJ,OAAO,EACV;IACG,qBACI,6LAAC;QAAO,WAAW,oKAAA,CAAA,UAAK,CAAC,MAAM;QAAE,SAAS;kBACrC;;;;;;AAGb;KATM;uCAWS", "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/main/ui/popularCountries.tsx"], "sourcesContent": ["import TextButton from '@/src/shared/ui/buttons/textButton'\nimport { FC } from 'react'\nimport style from './index.module.css'\n\nconst PopularCountries: FC = () => {\n    return (\n        <div className={`${style.card} ${style.popularCountries}`}>\n            <h2 className={style.title}>Популярные страны</h2>\n            <div className={style.loadMore}>\n                <TextButton text='Показать все страны' />\n            </div>\n        </div>\n    )\n}\n\nexport default PopularCountries\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,mBAAuB;IACzB,qBACI,6LAAC;QAAI,WAAW,AAAC,GAAgB,OAAd,oJAAA,CAAA,UAAK,CAAC,IAAI,EAAC,KAA0B,OAAvB,oJAAA,CAAA,UAAK,CAAC,gBAAgB;;0BACnD,6LAAC;gBAAG,WAAW,oJAAA,CAAA,UAAK,CAAC,KAAK;0BAAE;;;;;;0BAC5B,6LAAC;gBAAI,WAAW,oJAAA,CAAA,UAAK,CAAC,QAAQ;0BAC1B,cAAA,6LAAC,yJAAA,CAAA,UAAU;oBAAC,MAAK;;;;;;;;;;;;;;;;;AAIjC;KATM;uCAWS", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/main/ui/index.tsx"], "sourcesContent": ["'use client'\n\nimport { FC } from 'react'\nimport style from './index.module.css'\nimport PopularCountries from './popularCountries'\n\nconst MainContent: FC = () => {\n    return (\n        <main className={style.main}>\n            <PopularCountries />\n        </main>\n    )\n}\n\nexport default MainContent\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAMA,MAAM,cAAkB;IACpB,qBACI,6LAAC;QAAK,WAAW,oJAAA,CAAA,UAAK,CAAC,IAAI;kBACvB,cAAA,6LAAC,oJAAA,CAAA,UAAgB;;;;;;;;;;AAG7B;KANM;uCAQS", "debugId": null}}]}