{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/logo/index.module.css"], "sourcesContent": [".logo {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n}"], "names": [], "mappings": "AAAA", "debugId": null}}, {"offset": {"line": 8, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/header/ui/index.module.css"], "sourcesContent": [".header {\n    width: 100%;\n    height: 64px;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 10px 16px;\n    background: var(--surface);\n    box-shadow: 0 1px 0 var(--shadow);\n\n    position: fixed;\n    top: 0;\n    left: 0;\n    z-index: 100;\n}"], "names": [], "mappings": "AAAA", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/app/page.module.css"], "sourcesContent": [".page {\n\tdisplay: flex;\n\tflex-direction: column;\n\tmin-height: 100vh;\n}"], "names": [], "mappings": "AAAA", "debugId": null}}]}