{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/entities/country/ui/index.module.css"], "sourcesContent": [".card {\n\twidth: 100%;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tgap: 1rem;\n\theight: 3.625rem;\n\tpadding: 0 1rem;\n\tborder-radius: 1.25rem;\n\tbackground: var(--surface);\n}\n\n.content {\n\tdisplay: flex;\n\tgap: 1rem;\n\talign-items: center;\n}\n\n.flagWrapper {\n\twidth: 2rem;\n\theight: 2rem;\n\tborder-radius: 50%;\n\tbackground: var(--background);\n\toverflow: hidden;\n}\n\n.info {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: flex-start;\n\tgap: 0.125rem;\n}\n\n.title {\n\tfont-size: 0.875rem;\n\tfont-weight: 500;\n\tline-height: 1.125rem;\n}\n\n.price {\n\tfont-size: var(--font-caption-size);\n\tfont-weight: var(--font-caption-weight);\n\tline-height: var(--font-caption-line-height);\n\tletter-spacing: var(--font-caption-letter-spacing);\n}"], "names": [], "mappings": "AAAA;;;;;;;;;;;;AAYA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;AAMA"}}]}