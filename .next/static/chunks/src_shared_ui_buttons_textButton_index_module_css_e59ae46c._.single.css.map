{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/shared/ui/buttons/textButton/index.module.css"], "sourcesContent": [".button {\n\tfont-size: var(--font-body-size);\n\tfont-weight: 600;\n\tline-height: var(--font-body-line-height);\n\tletter-spacing: var(--font-body-letter-spacing);\n\tcolor: var(--surface);\n\tborder-radius: 1rem;\n\theight: 3.25rem;\n\twidth: 100%;\n\n\tbackground: var(--button-surface);\n\n\ttransition: background 0.2s ease;\n\n\t&:hover {\n\t\tbackground: var(--button-surface-hover);\n\t}\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;AAcC"}}]}