{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/logo/index.module.css"], "sourcesContent": [".logo {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 0.5rem;\n}\n"], "names": [], "mappings": "AAAA", "debugId": null}}, {"offset": {"line": 8, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/header/ui/index.module.css"], "sourcesContent": [".header {\n\twidth: 100%;\n\theight: 4rem;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 0.625rem 1rem;\n\tbackground: var(--surface);\n\tbox-shadow: 0 1px 0 var(--shadow);\n\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tz-index: 100;\n}\n"], "names": [], "mappings": "AAAA", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/index.module.css"], "sourcesContent": [".hero {\n\tpadding: 1.25rem 0 1rem 0;\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 2rem;\n}\n\n.title {\n\tfont-size: var(--font-title-size);\n\tfont-weight: var(--font-title-weight);\n\tline-height: var(--font-title-line-height);\n\tletter-spacing: var(--font-title-letter-spacing);\n\ttext-align: center;\n\ttext-wrap: balance;\n}\n\n.search {\n\tposition: relative;\n}\n\n.dropdown {\n\t&[data-is-visible='false'] {\n\t\topacity: 0;\n\t\tpointer-events: none;\n\t}\n\n\ttransition: opacity 0.2s ease;\n\topacity: 1;\n\tposition: absolute;\n\ttop: calc(100% + 0.375rem);\n\tleft: 0;\n\twidth: 100%;\n\tbackground: var(--surface);\n\tmin-height: 8.125rem;\n\tmax-height: 18.75rem;\n\n\tbox-shadow: 0 0.5rem 0.625rem 0.25rem rgba(0, 0, 0, 0.1);\n\n\tdisplay: flex;\n\tflex-direction: column;\n\tborder-radius: 1.25rem;\n}\n\n.notFound {\n\tmargin: auto;\n}"], "names": [], "mappings": "AAAA;;;;;;;AAOA;;;;;;;;;AASA;;;;AAKC;;;;;AAKW;;;;;;;;;;;;;;;;AAiBZ", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/input/index.module.css"], "sourcesContent": [".label {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 0.5rem;\n\tpadding: 0.75rem;\n\tbackground: var(--input-surface);\n\tborder-radius: 1rem;\n\tcursor: text;\n\ttransition: background 0.2s ease;\n\n\t&:focus-within {\n\t\tbackground: var(--input-surface-focus);\n\t}\n}\n\n.input {\n\tfont-size: var(--font-body-size);\n\tfont-weight: var(--font-body-weight);\n\tline-height: var(--font-body-line-height);\n\tletter-spacing: var(--font-body-letter-spacing);\n\tcolor: var(--foreground);\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;AAUC;;;;AAKD", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/howItWorks/ui/index.module.css"], "sourcesContent": [".card {\n    display: flex;\n    flex-direction: column;\n    gap: 1rem;\n    background: var(--surface);\n    border-radius: 1.25rem;\n\n    padding: 1rem;\n}\n\n.title {\n    font-size: var(--font-subtitle-size);\n    font-weight: var(--font-subtitle-weight);\n    line-height: var(--font-subtitle-line-height);\n    letter-spacing: var(--font-subtitle-letter-spacing);\n}"], "names": [], "mappings": "AAAA;;;;;;;;;AAUA", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/main/ui/index.module.css"], "sourcesContent": [".main {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 0.5rem;\n\tpadding: 0.5rem 0 1.5rem 0;\n}\n\n.card {\n\tdisplay: flex;\n\tflex-direction: column;\n\tbackground: var(--surface);\n\tborder-radius: 1.25rem;\n}\n\n.title {\n\tfont-size: var(--font-subtitle-size);\n\tfont-weight: var(--font-subtitle-weight);\n\tline-height: var(--font-subtitle-line-height);\n\tletter-spacing: var(--font-subtitle-letter-spacing);\n\n\tpadding: 1rem 1rem 0.75rem;\n}\n\n.loadMore {\n\tpadding: 0.75rem 1rem 1rem;\n}\n\n.popularCountries {}"], "names": [], "mappings": "AAAA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;AASA", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/buttons/textButton/index.module.css"], "sourcesContent": [".button {\n    font-size: var(--font-body-size);\n    font-weight: 600;\n    line-height: var(--font-body-line-height);\n    letter-spacing: var(--font-body-letter-spacing);\n    color: var(--surface);\n    border-radius: 1rem;\n    height: 3.25rem;\n    width: 100%;\n\n    background: var(--button-surface);\n\n    transition: background 0.2s ease;\n\n    &:hover {\n        background: var(--button-surface-hover);\n    }\n}"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;AAcI", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/pages/home/<USER>/index.module.css"], "sourcesContent": [".page {\n\tmin-height: 100vh;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.content {\n\tmax-width: 720px;\n\tpadding: 4rem 0.5rem 0 0.5rem;\n\tmargin: 0 auto;\n}\n"], "names": [], "mappings": "AAAA;;;;;;AAMA", "debugId": null}}]}