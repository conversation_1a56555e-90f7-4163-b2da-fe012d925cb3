{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/logo/index.module.css"], "sourcesContent": [".logo {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 0.5rem;\n}\n"], "names": [], "mappings": "AAAA", "debugId": null}}, {"offset": {"line": 8, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/header/ui/index.module.css"], "sourcesContent": [".header {\n\twidth: 100%;\n\theight: 4rem;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 0.625rem 1rem;\n\tbackground: var(--surface);\n\tbox-shadow: 0 1px 0 var(--shadow);\n\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tz-index: 100;\n}"], "names": [], "mappings": "AAAA", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/input/index.module.css"], "sourcesContent": [".label {\n    display: flex;\n    align-items: center;\n    gap: 0.5rem;\n    padding: 0.75rem;\n    background: var(--input-surface);\n    border-radius: 1rem;\n    cursor: text;\n    transition: background 0.2s ease;\n\n    &:focus-within {\n        background: var(--input-surface-focus);\n    }\n}\n\n.input {\n    font-size: var(--font-body-size);\n    font-weight: var(--font-body-weight);\n    line-height: var(--font-body-line-height);\n    letter-spacing: var(--font-body-letter-spacing);\n    color: var(--foreground);\n}"], "names": [], "mappings": "AAAA;;;;;;;;;;;AAUI;;;;AAKJ", "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/index.module.css"], "sourcesContent": [".hero {\n    padding: 1.25rem 0 1rem 0;\n    display: flex;\n    flex-direction: column;\n    gap: 2rem;\n}\n\n.title {\n    font-size: var(--font-title-size);\n    font-weight: var(--font-title-weight);\n    line-height: var(--font-title-line-height);\n    letter-spacing: var(--font-title-letter-spacing);\n    text-align: center;\n    text-wrap: balance;\n}"], "names": [], "mappings": "AAAA;;;;;;;AAOA", "debugId": null}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/pages/home/<USER>/index.module.css"], "sourcesContent": [".page {\n    min-height: 100vh;\n    display: flex;\n    flex-direction: column;\n}\n\n.content {\n    max-width: 720px;\n    padding: 4rem 0.5rem 0 0.5rem;\n    margin: 0 auto;\n}"], "names": [], "mappings": "AAAA;;;;;;AAMA", "debugId": null}}]}