/* [project]/src/shared/ui/logo/index.module.css [app-client] (css) */
.index-module__ytjyqG__logo {
  align-items: center;
  gap: .5rem;
  display: flex;
}

/* [project]/src/widgets/header/ui/index.module.css [app-client] (css) */
.index-module__6gvmdq__header {
  background: var(--surface);
  width: 100%;
  height: 4rem;
  box-shadow: 0 1px 0 var(--shadow);
  z-index: 100;
  justify-content: space-between;
  align-items: center;
  padding: .625rem 1rem;
  display: flex;
  position: fixed;
  top: 0;
  left: 0;
}

/* [project]/src/shared/ui/input/index.module.css [app-client] (css) */
.index-module__Bu1eGa__label {
  background: var(--input-surface);
  cursor: text;
  border-radius: 1rem;
  align-items: center;
  gap: .5rem;
  padding: .75rem;
  transition: background .2s;
  display: flex;
}

.index-module__Bu1eGa__label:focus-within {
  background: var(--input-surface-focus);
}

.index-module__Bu1eGa__input {
  font-size: var(--font-body-size);
  font-weight: var(--font-body-weight);
  line-height: var(--font-body-line-height);
  letter-spacing: var(--font-body-letter-spacing);
  color: var(--foreground);
}

/* [project]/src/widgets/hero/ui/index.module.css [app-client] (css) */
.index-module__m10heG__hero {
  flex-direction: column;
  gap: 2rem;
  padding: 1.25rem 0 1rem;
  display: flex;
}

.index-module__m10heG__title {
  font-size: var(--font-title-size);
  font-weight: var(--font-title-weight);
  line-height: var(--font-title-line-height);
  letter-spacing: var(--font-title-letter-spacing);
  text-align: center;
  text-wrap: balance;
}

/* [project]/src/pages/home/<USER>/index.module.css [app-client] (css) */
.index-module__vhjfEW__page {
  flex-direction: column;
  min-height: 100vh;
  display: flex;
}

.index-module__vhjfEW__content {
  max-width: 720px;
  margin: 0 auto;
  padding: 4rem .5rem 0;
}

/*# sourceMappingURL=src_5b239afc._.css.map*/