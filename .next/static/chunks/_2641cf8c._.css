/* [project]/src/shared/ui/logo/index.module.css [app-client] (css) */
.index-module__ytjyqG__logo {
  align-items: center;
  gap: 8px;
  display: flex;
}

/* [project]/src/widgets/header/ui/index.module.css [app-client] (css) */
.index-module__6gvmdq__header {
  background: var(--surface);
  width: 100%;
  height: 64px;
  box-shadow: 0 1px 0 var(--shadow);
  z-index: 100;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  display: flex;
  position: fixed;
  top: 0;
  left: 0;
}

/* [project]/app/page.module.css [app-client] (css) */
.page-module__E0kJGG__page {
  flex-direction: column;
  min-height: 100vh;
  display: flex;
}

/*# sourceMappingURL=_2641cf8c._.css.map*/