{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/icons/search.tsx"], "sourcesContent": ["import Image from 'next/image'\nimport { FC } from 'react'\n\nconst SearchIcon: FC = () => {\n\treturn (\n\t\t<Image src={'/search-icon.svg'} alt='Search' width={20} height={20} />\n\t)\n}\n\nexport default SearchIcon\n"], "names": [], "mappings": ";;;;AAAA;;;AAGA,MAAM,aAAiB;IACtB,qBACC,6LAAC,gIAAA,CAAA,UAAK;QAAC,KAAK;QAAoB,KAAI;QAAS,OAAO;QAAI,QAAQ;;;;;;AAElE;KAJM;uCAMS", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/shared/ui/input/index.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"input\": \"index-module__Bu1eGa__input\",\n  \"label\": \"index-module__Bu1eGa__label\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/input/index.tsx"], "sourcesContent": ["import { FC, HTMLInputTypeAttribute, ReactNode } from 'react'\nimport style from './index.module.css'\n\ninterface InputProps {\n\ttype?: HTMLInputTypeAttribute\n\tname?: string\n\tvalue?: string\n\tplaceholder?: string\n\tprefix?: ReactNode\n\tonChange?: (event: React.ChangeEvent<HTMLInputElement>) => void\n}\n\nconst Input: FC<InputProps> = ({\n\ttype,\n\tname,\n\tvalue,\n\tplaceholder,\n\tprefix,\n\tonChange\n}) => {\n\treturn (\n\t\t<label className={style.label}>\n\t\t\t{prefix}\n\t\t\t<input\n\t\t\t\tclassName={style.input}\n\t\t\t\ttype={type}\n\t\t\t\tname={name}\n\t\t\t\tvalue={value}\n\t\t\t\tplaceholder={placeholder}\n\t\t\t\tonChange={onChange}\n\t\t\t/>\n\t\t</label>\n\t)\n}\n\nexport default Input\n"], "names": [], "mappings": ";;;;AACA;;;AAWA,MAAM,QAAwB;QAAC,EAC9B,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,WAAW,EACX,MAAM,EACN,QAAQ,EACR;IACA,qBACC,6LAAC;QAAM,WAAW,oJAAA,CAAA,UAAK,CAAC,KAAK;;YAC3B;0BACD,6LAAC;gBACA,WAAW,oJAAA,CAAA,UAAK,CAAC,KAAK;gBACtB,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;;;;;;;;;;;;AAId;KArBM;uCAuBS", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/model/searchStore.ts"], "sourcesContent": ["\nimport { create } from 'zustand';\n\ninterface SearchStore {\n    searchQuery: string;\n    setSearchQuery: (value: string) => void;\n}\n\nexport const useSearchStore = create<SearchStore>((set) => ({\n    searchQuery: '',\n    setSearchQuery: (value) => set({ searchQuery: value }),\n}))\n"], "names": [], "mappings": ";;;AACA;;AAOO,MAAM,iBAAiB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAe,CAAC,MAAQ,CAAC;QACxD,aAAa;QACb,gBAAgB,CAAC,QAAU,IAAI;gBAAE,aAAa;YAAM;IACxD,CAAC", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/hero/ui/index.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"dropdown\": \"index-module__m10heG__dropdown\",\n  \"hero\": \"index-module__m10heG__hero\",\n  \"notFound\": \"index-module__m10heG__notFound\",\n  \"search\": \"index-module__m10heG__search\",\n  \"title\": \"index-module__m10heG__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/searchDropdown.tsx"], "sourcesContent": ["import { FC } from 'react';\nimport style from './index.module.css';\n\ninterface SearchDropdownProps {\n    searchQuery: string;\n}\n\nconst SearchDropdown: FC<SearchDropdownProps> = ({\n    searchQuery,\n}) => {\n    return (\n        <div className={style.dropdown} data-is-visible={!!searchQuery.length}>\n            <span className={style.notFound}>\n                Ничего не найдено :(\n            </span>\n        </div>\n    );\n}\n\nexport default SearchDropdown;"], "names": [], "mappings": ";;;;AACA;;;AAMA,MAAM,iBAA0C;QAAC,EAC7C,WAAW,EACd;IACG,qBACI,6LAAC;QAAI,WAAW,oJAAA,CAAA,UAAK,CAAC,QAAQ;QAAE,mBAAiB,CAAC,CAAC,YAAY,MAAM;kBACjE,cAAA,6LAAC;YAAK,WAAW,oJAAA,CAAA,UAAK,CAAC,QAAQ;sBAAE;;;;;;;;;;;AAK7C;KAVM;uCAYS", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/search.tsx"], "sourcesContent": ["'use client'\n\nimport SearchIcon from \"@/src/shared/ui/icons/search\"\nimport Input from \"@/src/shared/ui/input\"\nimport { useSearchStore } from \"../model/searchStore\"\nimport style from './index.module.css'\nimport SearchDropdown from \"./searchDropdown\"\n\nconst HeroSearch = () => {\n    const { searchQuery, setSearchQuery } = useSearchStore()\n\n    const handleSearch = (value: string) => {\n        setSearchQuery(value)\n        console.log(value)\n    }\n\n    return (\n        <div className={style.search}>\n            <Input\n                placeholder='Найти направление'\n                prefix={<SearchIcon />}\n                value={searchQuery}\n                onChange={(value) => handleSearch(value.target.value)}\n            />\n            <SearchDropdown searchQuery={searchQuery} />\n        </div>\n    )\n}\n\nexport default HeroSearch;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQA,MAAM,aAAa;;IACf,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD;IAErD,MAAM,eAAe,CAAC;QAClB,eAAe;QACf,QAAQ,GAAG,CAAC;IAChB;IAEA,qBACI,6LAAC;QAAI,WAAW,oJAAA,CAAA,UAAK,CAAC,MAAM;;0BACxB,6LAAC,yIAAA,CAAA,UAAK;gBACF,aAAY;gBACZ,sBAAQ,6LAAC,0IAAA,CAAA,UAAU;;;;;gBACnB,OAAO;gBACP,UAAU,CAAC,QAAU,aAAa,MAAM,MAAM,CAAC,KAAK;;;;;;0BAExD,6LAAC,kJAAA,CAAA,UAAc;gBAAC,aAAa;;;;;;;;;;;;AAGzC;GAnBM;;QACsC,iJAAA,CAAA,iBAAc;;;KADpD;uCAqBS", "debugId": null}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/pages/home/<USER>/countries.ts"], "sourcesContent": ["// zustand store\nimport { CountriesI } from '@/src/entities/country/model/country';\nimport { create } from 'zustand';\n\ninterface CountriesStore {\n    countries: CountriesI;\n    setCountries: (countries: CountriesI) => void;\n}\n\nexport const useCountriesStore = create<CountriesStore>((set) => ({\n    countries: [],\n    setCountries: (countries) => set({ countries }),\n}))\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;AAEhB;;AAOO,MAAM,oBAAoB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAkB,CAAC,MAAQ,CAAC;QAC9D,WAAW,EAAE;QACb,cAAc,CAAC,YAAc,IAAI;gBAAE;YAAU;IACjD,CAAC", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/main/ui/mainContentClient.tsx"], "sourcesContent": ["'use client'\n\nimport { CountriesI } from '@/src/entities/country/model/country'\nimport { useCountriesStore } from '@/src/pages/home/<USER>/countries'\nimport { FC, useEffect } from 'react'\n\ninterface MainContentClientProps {\n\tcountries: CountriesI\n}\n\nconst MainContentClient: FC<MainContentClientProps> = ({ countries }) => {\n\tconst { setCountries } = useCountriesStore()\n\n\t// Устанавливаем данные в store для клиентской части\n\tuseEffect(() => {\n\t\tsetCountries(countries)\n\t}, [countries, setCountries])\n\n\t// Этот компонент не рендерит ничего, только управляет состоянием\n\treturn null\n}\n\nexport default MainContentClient\n"], "names": [], "mappings": ";;;AAGA;AACA;;AAJA;;;AAUA,MAAM,oBAAgD;QAAC,EAAE,SAAS,EAAE;;IACnE,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,oBAAiB,AAAD;IAEzC,oDAAoD;IACpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACT,aAAa;QACd;sCAAG;QAAC;QAAW;KAAa;IAE5B,iEAAiE;IACjE,OAAO;AACR;GAVM;;QACoB,6IAAA,CAAA,oBAAiB;;;KADrC;uCAYS", "debugId": null}}]}