/* [project]/app/page.module.css [app-client] (css) */
.page-module__E0kJGG__page {
  --gray-rgb: 0, 0, 0;
  --gray-alpha-200: rgba(var(--gray-rgb), .08);
  --gray-alpha-100: rgba(var(--gray-rgb), .05);
  --button-primary-hover: #383838;
  --button-secondary-hover: #f2f2f2;
  min-height: 100svh;
  font-family: var(--font-geist-sans);
  grid-template-rows: 20px 1fr 20px;
  place-items: center;
  gap: 64px;
  padding: 80px;
  display: grid;
}

@media (prefers-color-scheme: dark) {
  .page-module__E0kJGG__page {
    --gray-rgb: 255, 255, 255;
    --gray-alpha-200: rgba(var(--gray-rgb), .145);
    --gray-alpha-100: rgba(var(--gray-rgb), .06);
    --button-primary-hover: #ccc;
    --button-secondary-hover: #1a1a1a;
  }
}

.page-module__E0kJGG__main {
  flex-direction: column;
  grid-row-start: 2;
  gap: 32px;
  display: flex;
}

.page-module__E0kJGG__main ol {
  font-family: var(--font-geist-mono);
  letter-spacing: -.01em;
  margin: 0;
  padding-left: 0;
  font-size: 14px;
  line-height: 24px;
  list-style-position: inside;
}

.page-module__E0kJGG__main li:not(:last-of-type) {
  margin-bottom: 8px;
}

.page-module__E0kJGG__main code {
  background: var(--gray-alpha-100);
  border-radius: 4px;
  padding: 2px 4px;
  font-family: inherit;
  font-weight: 600;
}

.page-module__E0kJGG__ctas {
  gap: 16px;
  display: flex;
}

.page-module__E0kJGG__ctas a {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  cursor: pointer;
  border: 1px solid rgba(0, 0, 0, 0);
  border-radius: 128px;
  justify-content: center;
  align-items: center;
  height: 48px;
  padding: 0 20px;
  font-size: 16px;
  font-weight: 500;
  line-height: 20px;
  transition: background .2s, color .2s, border-color .2s;
  display: flex;
}

a.page-module__E0kJGG__primary {
  background: var(--foreground);
  color: var(--background);
  gap: 8px;
}

a.page-module__E0kJGG__secondary {
  border-color: var(--gray-alpha-200);
  min-width: 158px;
}

.page-module__E0kJGG__footer {
  grid-row-start: 3;
  gap: 24px;
  display: flex;
}

.page-module__E0kJGG__footer a {
  align-items: center;
  gap: 8px;
  display: flex;
}

.page-module__E0kJGG__footer img {
  flex-shrink: 0;
}

@media (hover: hover) and (pointer: fine) {
  a.page-module__E0kJGG__primary:hover {
    background: var(--button-primary-hover);
    border-color: rgba(0, 0, 0, 0);
  }

  a.page-module__E0kJGG__secondary:hover {
    background: var(--button-secondary-hover);
    border-color: rgba(0, 0, 0, 0);
  }

  .page-module__E0kJGG__footer a:hover {
    text-underline-offset: 4px;
    text-decoration: underline;
  }
}

@media (max-width: 600px) {
  .page-module__E0kJGG__page {
    padding: 32px 32px 80px;
  }

  .page-module__E0kJGG__main {
    align-items: center;
  }

  .page-module__E0kJGG__main ol {
    text-align: center;
  }

  .page-module__E0kJGG__ctas {
    flex-direction: column;
  }

  .page-module__E0kJGG__ctas a {
    height: 40px;
    padding: 0 16px;
    font-size: 14px;
  }

  a.page-module__E0kJGG__secondary {
    min-width: auto;
  }

  .page-module__E0kJGG__footer {
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
  }
}

@media (prefers-color-scheme: dark) {
  .page-module__E0kJGG__logo {
    filter: invert();
  }
}

/*# sourceMappingURL=app_page_module_24d20b32.css.map*/