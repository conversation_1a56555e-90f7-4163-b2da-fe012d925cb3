/* [project]/app/globals.css [app-client] (css) */
:root {
  --background: #f7f7f7;
  --foreground: #000;
  --shadow: rgba(0, 0, 0, .05);
  --surface: #fff;
  --font-title-weight: 700;
  --font-title-size: 1.75rem;
}

html, body {
  max-width: 100vw;
  overflow-x: hidden;
}

html {
  font-size: 16px;
}

body {
  color: var(--foreground);
  background: var(--background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: Inter, Helvetica, sans-serif;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

a {
  color: inherit;
  text-decoration: none;
}

/*# sourceMappingURL=app_globals_73c37791.css.map*/