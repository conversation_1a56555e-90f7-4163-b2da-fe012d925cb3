{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/app/globals.css"], "sourcesContent": [":root {\n\t--background: #f7f7f7;\n\t--foreground: #000000;\n\t--shadow: #0000000d;\n\t--surface: #ffffff;\n\n\t--font-title-weight: 700;\n\t--font-title-size: 1.75rem;\n}\n\nhtml,\nbody {\n\tmax-width: 100vw;\n\toverflow-x: hidden;\n}\n\nhtml {\n\tfont-size: 16px;\n}\n\nbody {\n\tcolor: var(--foreground);\n\tbackground: var(--background);\n\tfont-family: 'Inter', Helvetica, sans-serif;\n\t-webkit-font-smoothing: antialiased;\n\t-moz-osx-font-smoothing: grayscale;\n}\n\n* {\n\tbox-sizing: border-box;\n\tpadding: 0;\n\tmargin: 0;\n}\n\na {\n\tcolor: inherit;\n\ttext-decoration: none;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;AAUA;;;;;AAMA;;;;AAIA;;;;;;;;AAQA;;;;;;AAMA", "debugId": null}}]}