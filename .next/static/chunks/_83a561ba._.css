/* [project]/src/widgets/header/ui/index.module.css [app-client] (css) */
.index-module__6gvmdq__header {
  background: var(--surface);
  width: 100%;
  height: 64px;
  box-shadow: 0 1px 0 var(--shadow);
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  display: flex;
}

/* [project]/app/page.module.css [app-client] (css) */
.page-module__E0kJGG__page {
  flex-direction: column;
  min-height: 100vh;
  display: flex;
}

/*# sourceMappingURL=_83a561ba._.css.map*/