{"semi": false, "singleQuote": true, "jsxSingleQuote": true, "trailingComma": "none", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "htmlWhitespaceSensitivity": "css", "endOfLine": "lf", "quoteProps": "as-needed", "vueIndentScriptAndStyle": false, "embeddedLanguageFormatting": "auto", "singleAttributePerLine": false, "experimentalOperatorPosition": "start", "experimentalTernaries": false, "jsxBracketSameLine": false, "tabWidth": 4, "useTabs": true}